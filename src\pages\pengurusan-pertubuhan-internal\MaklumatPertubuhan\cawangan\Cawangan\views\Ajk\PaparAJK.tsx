import { Box, Grid, Theme, Typography, useMediaQuery } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import DisabledTextField from "../../../../../../../components/input/DisabledTextField";
import { t } from "i18next";
import { ButtonPrimary } from "../../../../../../../components/button";
import { designation, ListGelaran } from "@/helpers";

const PaparAJK = () => {
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
    "& span": {
      color: "red",
    },
  };

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const location = useLocation();
  const committee = location.state?.committee;
  const addressData = location.state?.addressData;
  const branchDetailData = location.state?.branchDetailData;

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {branchDetailData.societyName ?? "-"} <br />
              {branchDetailData.societyNo ?? "-"}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("pengerusi")}
            </Typography>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("position")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={designation[committee.designationCode].name ?? "-"}
                />
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("chairmanPersonalInfo")}
            </Typography>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("idType")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={committee.identificationType ?? "-"}
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("idNumber")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.identificationNo ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("title")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    ListGelaran.find(
                      (item) => item.value === committee.titleCode
                    )?.label ?? "-"
                  }
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("fullName")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.name ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("gender")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.gender ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("citizenship")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.nationalityStatus ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("dateOfBirth")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.dateOfBirth ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("placeOfBirth")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.placeOfBirth ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("occupation")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.jobCode ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("residentialAddress")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={committee.residentialAddress ?? "-"}
                  multiline
                  row={3}
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("negeri")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    addressData.find(
                      (id: any) =>
                        id.id === parseInt(committee.residentialStateCode)
                    )?.name ?? "-"
                  }
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("daerah")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    addressData.find(
                      (id: any) =>
                        id.id === parseInt(committee.residentialDistrictCode)
                    )?.name ?? "-"
                  }
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("poskod")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={committee.residentialPostcode ?? "-"}
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("emel")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.email ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("phoneNumber")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.phoneNumber ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("nomborTelefonRumah")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.telephoneNumber ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("nomborTelefonPejabat")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.telephoneNumber ?? "-"} />
              </Grid>
            </Grid>
          </Box>

          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("employerInfo")}
            </Typography>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("employerName")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.employerName ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("employerAddress")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value="" multiline row={3} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("negara")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    addressData.find(
                      (id: any) =>
                        id.id === parseInt(committee.employerCountryCode)
                    )?.name ?? "-"
                  }
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("negeri")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    addressData.find(
                      (id: any) =>
                        id.id === parseInt(committee.employerStateCode)
                    )?.name ?? "-"
                  }
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("daerah")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={
                    addressData.find(
                      (id: any) =>
                        id.id === parseInt(committee.employerDistrictCode)
                    )?.name ?? "-"
                  }
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("bandar")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.employerCity ?? "-"} />
              </Grid>
            </Grid>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("poskod")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={committee.employerPostcode ?? "-"} />
              </Grid>
            </Grid>
          </Box>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary
              type="submit"
              sx={{
                display: "block",
                backgroundColor: "var(--primary-color)",
                width: "100px",
                minWidth: "unset",
                height: "32px",
                color: "white",
                "&:hover": { backgroundColor: "#19ADAD" },
                textTransform: "none",
                fontWeight: 400,
                fontSize: "8px",
              }}
              onClick={() => navigate(-1)}
            >
              {t("back")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default PaparAJK;
