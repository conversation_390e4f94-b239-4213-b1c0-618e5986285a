import {
  Box,
  Button,
  FormControl,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { t } from "i18next";
import { LaranganBox } from "./component/LaranganBorder";
import { LaranganPaper } from "./component/LaranganPaper";
import "./larangan.css";
import { useState } from "react";
import { SearchIcon } from "@/components/icons";
import { FilterList, KeyboardArrowDown } from "@mui/icons-material";
import { values } from "lodash";

export const SenaraiNamaLarangan = () => {
  const theme = useTheme();
  const primary = theme.palette.primary.main;
  const [namaLaranganTab, setNamaLaranganTab] = useState(0);
  // const [activeNamaLarangan, setActiveNamaLarangan] = useState()
  const [selectedActiveOption, setSelectedActiveOption] = useState("");

  const activeFilterOption = [
    {
      value: "all",
      status: "<PERSON><PERSON><PERSON>",
    },
    {
      value: "active",
      status: "Aktif",
    },
    {
      value: "inactive",
      status: "Tidak Aktif",
    },
  ];

  const title = [
    {
      index: 0,
      title: t("Nama Larangan"),
    },
    {
      index: 1,
      title: t("Senarai Kelabu"),
    },
  ];

  return (
    <>
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 2,
        }}
      >
        <LaranganPaper
          sx={{
            padding: "5px",
          }}
        >
          <>
            <Box
              sx={{
                width: "100%",
                display: "flex",
                // gap: 1,
                // p: 0.5,
                borderRadius: "10px",
              }}
            >
              <Button
                className="larangan-tab-btn"
                fullWidth
                variant={namaLaranganTab == 0 ? "contained" : "text"}
                onClick={() => setNamaLaranganTab(0)}
              >
                Nama Larangan
              </Button>
              <Button
                className="larangan-tab-btn"
                fullWidth
                variant={namaLaranganTab == 1 ? "contained" : "text"}
                onClick={() => setNamaLaranganTab(1)}
              >
                Senarai Kelabu
              </Button>
            </Box>
          </>
        </LaranganPaper>
        <LaranganPaper>
          <LaranganBox>
            <Typography
              color={primary}
              sx={{
                fontSize: 14,
                fontWeight: "medium",
                marginBottom: "1.5rem",
              }}
            >
              {title[namaLaranganTab].title}
            </Typography>
            <Box
              sx={{
                mr: "auto",
                ml: "auto",
                width: "70%",
                display: "flex",
                flexDirection: "column",
                // gap: 1,
              }}
            >
              <Box sx={{ mb: 2, width: "100%" }}>
                <TextField
                  placeholder="Nama Acara"
                  variant="outlined"
                  fullWidth
                  size="small"
                  // value={searchTerm}
                  // onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon sx={{ color: "#9CA3AF" }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: "10px",
                      "& fieldset": {
                        borderColor: "#E5E7EB",
                      },
                      "&:hover fieldset": {
                        borderColor: "#E5E7EB",
                      },
                      "&.Mui-focused fieldset": {
                        borderColor: "#E5E7EB",
                      },
                    },
                  }}
                />
              </Box>
              <Box
                sx={{
                  // border: "1px solid #E5E7EB",
                  borderRadius: "30px",
                  // padding: "30px 20px 20px 20px",
                  // boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
                }}
              >
                <Box
                  sx={{
                    display: "flex",
                    gap: 2,
                    justifyContent: "center",
                    ml: "auto",
                    mr: "auto",
                    width: "70%",
                    borderRadius: "20px",
                    boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
                    p: "3px",
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      color: "#6B7280",
                      borderRight: "1px solid #E5E7EB",
                      pr: 2,
                      height: "40px",
                    }}
                  >
                    <FilterList sx={{ fontSize: 20, mr: 1 }} />
                    <Typography variant="body2">
                      Tapis Berdasarkan
                    </Typography>
                  </Box>
                  <FormControl
                    size="small"
                    sx={{
                      minWidth: 120,
                      borderRight: "1px solid #E5E7EB",
                      height: "40px",
                      // paddingRight: 2,
                      // marginRight: 2
                    }}
                  >
                    <Select
                      displayEmpty
                      placeholder="Status Paparan"
                      value={selectedActiveOption || ""}
                      onChange={(e) =>
                        setSelectedActiveOption(e.target.value)
                        // handleActiveOptionChange(e.target.value);
                      }
                      IconComponent={(props) => (
                        <KeyboardArrowDown {...props} sx={{ marginLeft: 1 }} />
                      )}
                      sx={{
                        border: "none",
                        "& .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          border: "none",
                        },
                        "& .MuiSelect-select": {
                          paddingRight: "32px", // Make room for the dropdown icon
                          fontWeight: 400, // Normal font weight instead of bold
                          fontSize: "14px",
                          color: "#6B7280",
                        },
                      }}
                      renderValue={(selected) => {
                        if (!selected) return "Status Paparan";
                        const option = activeFilterOption.find(
                          (opt) => opt.value == selected
                        );
                        return option ? option.status : "Status Paparan";
                      }}
                    >
                      {/* <MenuItem defaultValue={-1} value={-1}>
                        <em>Status Paparan</em>
                      </MenuItem> */}
                      {activeFilterOption.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.status}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              </Box>
            </Box>
          </LaranganBox>
        </LaranganPaper>
      </Box>
    </>
  );
};
