import { Box, IconButton, Typography } from "@mui/material";
import { t } from "i18next";
import { useState } from "react";
import { ButtonOutline } from "../../../../../../../components/button";
import { EyeIcon } from "../../../../../../../components/icons";
import { useNavigate, useParams } from "react-router-dom";
import { usePertubuhanContext } from "../../PertubuhanProvider";
import { OrganisationPositions, useQuery } from "@/helpers";
import { FieldValues, useForm } from "react-hook-form";
import { DataTable, IColumn } from "@/components";

const SenaraiAJK = () => {
  const subTitleStyle = {
    color: "var(--primary-color)",
    pb: "30px",
    fontSize: "16px",
    fontWeight: "500 !important",
  };

  const { setCommittee, addressData, societyDetailData } =
    usePertubuhanContext();
  const [ajkList, setAjkList] = useState();
  const [ajkListTotal, setAjkListTotal] = useState(0);

  const form = useForm<any>();
  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 5,
    },
  });
  const page = watch("page");
  const pageSize = watch("pageSize");

  const { id } = useParams();
  const decodedId = atob(id || "");
  const { data: listCommittee, isLoading: isLoadingCommittee } = useQuery({
    url: `society/committee/getAll`,
    autoFetch: true,
    filters: [
      { field: "societyId", value: decodedId, operator: "eq" },
      {
        field: "pageNo",
        operator: "eq",
        value: page,
      },
      {
        field: "pageSize",
        operator: "eq",
        value: pageSize,
      },
    ],
    onSuccess: (data) => {
      setAjkList(data?.data?.data?.data || []);
      setAjkListTotal(listCommittee?.data?.data?.total);
    },
  });

  const navigate = useNavigate();

  const handleView = (committee: any) => {
    setCommittee(committee);
    navigate("../induk/pertubuhan/ajk/papar", {
      state: {
        committee: committee,
        addressData: addressData,
        societyDetailData: societyDetailData,
      },
    });
  };

  const columnsCitizen: IColumn[] = [
    {
      field: "designationCode",
      headerName: t("position"),
      align: "center",
      flex: 1,
      renderCell: ({ row }: any) => {
        return t(
          `${
            OrganisationPositions.find(
              (item) => item?.value === Number(row?.designationCode)
            )?.label || "-"
          }`
        );
      },
    },
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.name ? row?.name : "-";
      },
    },
    {
      field: "identificationNo",
      headerName: t("identificationNumber"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return row.identificationNo ? row.identificationNo : "-";
      },
    },
    {
      field: "email",
      headerName: t("email"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return row?.email ? row?.email : "-";
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      headerAlign: "right",
      align: "right",
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <>
            <IconButton onClick={() => handleView(row)}>
              <EyeIcon
                sx={{
                  fontSize: "2rem",
                  width: "1rem",
                  height: "1rem",
                  color: "var(--grey-text)",
                }}
              />
            </IconButton>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          padding: "18px 16px",
          borderRadius: "14px",
          marginBottom: 1,
        }}
      >
        <Box
          sx={{
            borderRadius: "10px",
            padding: "41px 25px 25px",
            border: "0.5px solid #DADADA",
            marginBottom: "13px",
          }}
        >
          <Typography sx={subTitleStyle}>{t("ajkList")}</Typography>

          <Box
            sx={{
              display: "flex",
              alignItems: "flex-start",
              justifyContent: "flex-end",
              gap: 2,
            }}
          >
            <Box
              sx={{
                background: "var(--primary-color)",
                cursor: "pointer",
                display: "flex",
                justifyContent: "center",
                alignContent: "center",
                borderRadius: "10px",
                p: 1,
                flexShrink: 0,
                height: "100%",
                mb: 2,
              }}
            >
              <img width={26} height={26} src="/addkuiri.svg" alt="" />
            </Box>
            <ButtonOutline
              startIcon={<></>}
              sx={{ alignSelf: "flex-start", px: 2, py: 1 }}
              onClick={() =>
                navigate(`../induk/pertubuhan/ajk/${id}`, {
                  state: {
                    societyDetailData: societyDetailData,
                    // listAjk: listAjk,
                  },
                })
              }
            >
              {t("viewAjk")}
            </ButtonOutline>
          </Box>
          <DataTable
            columns={columnsCitizen}
            rows={ajkList ?? []}
            page={page}
            rowsPerPage={pageSize}
            totalCount={ajkListTotal}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("page", 1);
              setValue("pageSize", newPageSize);
            }}
            isLoading={isLoadingCommittee}
          />
        </Box>
      </Box>
    </>
  );
};

export default SenaraiAJK;
