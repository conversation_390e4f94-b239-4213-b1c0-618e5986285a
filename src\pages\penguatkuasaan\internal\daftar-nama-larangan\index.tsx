import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Route, Routes, useNavigate } from "react-router-dom";
import { PORTAL_INTERNAL, useQuery } from "@/helpers";
import { NavbarEnforcement } from "@/components/navbar/Enforcement";
import { Box, Container, Drawer, Grid } from "@mui/material";
import { LaranganTab } from "./namaLaranganRoutes";
import { LaranganPaper } from "./component/LaranganPaper";

const DaftarNamaLarangan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [activeLaranganContent, setActiveLaranganContent] = useState<number>(0);
  const [page, setPage] = useState(1);

  const renderContent = () => {
    switch (activeLaranganContent) {
      case 0:
        return <h2><PERSON><PERSON><PERSON></h2>;
      case 1:
        return <h2>Sen<PERSON>i <PERSON>n Lo<PERSON></h2>;
      case 2:
        return <h2>Cipta Rekod</h2>;
      default:
        return <h2>Senarai Nama Larangan</h2>;
    }
  };

  return (
    <>
      <Grid
        sx={{
          display: "grid",
          gap: 2,
        }}
      >
        <Grid item md={12.5}>
          <LaranganPaper>{LaranganTab()}</LaranganPaper>
        </Grid>
        <Grid item md={12.5}>
          <LaranganPaper></LaranganPaper>
        </Grid>
      </Grid>
    </>
  );
};

export default DaftarNamaLarangan;
