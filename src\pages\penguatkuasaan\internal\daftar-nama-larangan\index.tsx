import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Route, Routes, useNavigate } from "react-router-dom";
import { PORTAL_INTERNAL, useQuery } from "@/helpers";
import { NavbarEnforcement } from "@/components/navbar/Enforcement";
import { Box, Container, Drawer, Grid } from "@mui/material";
import { LaranganTab } from "./laranganTab";
import { LaranganPaper } from "./component/LaranganPaper";
import { useLaranganContext } from "@/contexts/laranganProvider";
import { SenaraiNamaLarangan } from "./senaraiNamaLarangan";
import { SenaraiLogoLarangan } from "./senaraiLogoLarangan";
import { TambahRekodLarangan } from "./tambahRekodLarangan";

const DaftarNamaLarangan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const {activeLaranganContent, setActiveLaranganContent} = useLaranganContext();
  const [page, setPage] = useState(1);

  const renderContent = () => {
    switch (activeLaranganContent) {
      case 0:
        return <SenaraiNamaLarangan />;
      case 1:
        return <SenaraiLogoLarangan />;
      case 2:
        return <TambahRekodLarangan />;
      default:
        return <SenaraiNamaLarangan />;
    }
  };

  return (
    <>
      <Grid
        sx={{
          display: "grid",
          gap: 2,
        }}
      >
        <Grid item md={12.5}>
          <LaranganPaper>{LaranganTab()}</LaranganPaper>
        </Grid>
        <Grid item md={12.5}>
            {renderContent()}
        </Grid>
      </Grid>
    </>
  );
};

export default DaftarNamaLarangan;
