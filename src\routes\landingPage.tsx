import { lazy, Suspense } from "react";
import { Outlet, Route } from "react-router-dom";
import PanduanPengguna from "../pages/landing-page/panduanpengguna";
import MaklumBalas from "../pages/landing-page/maklumbalas";
import Pewartaan from "../pages/landing-page/pewartaan";
import MainLanding from "../pages/landing-page/mainLanding";
import CalendarPublicPage from "../pages/calendar/public/Main";
import CalendarPublicActivitySummary from "../pages/calendar/public/ActivitySummary";
import CalendarPublicActivityListsPage from "../pages/calendar/public/ActivityLists";
import CalendarPublicActivityDetailsPage from "../pages/calendar/public/ActivityDetails";
import MainLandingLayout from "@/pages/landing-page/mainLanding/mainLandingLayout";
import Artikel from "@/pages/artikel";
import TakwimLandingPage from "@/pages/takwim/TakwimLandingPage";
import TakwimActivityPage from "../pages/takwim/ActivityDetails";
import TakwimActivitySummaryPage from "../pages/takwim/ActivitySummary";
import TakwimActivityListsPage from "../pages/takwim/ActivityLists";
import TakwimActivityDetailsPage from "../pages/takwim/ActivityDetails";
import CreateEventPage from "@/pages/takwim/CreateEvent";
import TakwimProvider from "@/contexts/takwimProvider";
import UpdateAttendance from "@/pages/takwim/UpdateAttendance";
import TermaPenggunaan from "@/pages/landing-page/mainLanding/termaPenggunaan";

import { PageLoader } from "@/components";

const DokumenJPPM = lazy(() => import("@/pages/dokumen-jppm"));


export const landingPage = {
  routes: (
    <>
      <Route
        path="/main"
        element={
          <MainLandingLayout>
            <MainLanding />
          </MainLandingLayout>
        }
      />
      <Route
        path="/artikel/:slug"
        element={
          <MainLandingLayout>
            <Artikel />
          </MainLandingLayout>
        }
      />
      <Route path="/panduan-pengguna" element={<PanduanPengguna />} />
      <Route path="/maklumbalas" element={<MaklumBalas />} />
      <Route path="/pewartaan" element={<Pewartaan />} />

      {/* Takwim routes */}
      <Route
        path="/takwim"
        element={
          <TakwimProvider>
            <TakwimLandingPage />
          </TakwimProvider>
        }
      >
        <Route index element={<TakwimActivityListsPage />} />
        <Route path="activity" element={<TakwimActivityListsPage />} />
        <Route
          path="activity/:eventNo"
          element={<TakwimActivityDetailsPage />}
        />
        <Route path="create-event" element={<CreateEventPage />} />
        <Route path="edit-event/:eventNo" element={<CreateEventPage />} />
        {/* <Route path="update-attendance" element={<UpdateAttendance />} /> */}
        <Route path="terma-penggunaan" element={<TermaPenggunaan />} />
      </Route>

        <Route
          path="/dokumen-jppm"
          element={
            <Suspense fallback={<PageLoader />}>
              <MainLandingLayout>
                <DokumenJPPM />
              </MainLandingLayout>
            </Suspense>
          }
        />

      {/* Attendance and Feedback routes */}
    </>
  ),
};

