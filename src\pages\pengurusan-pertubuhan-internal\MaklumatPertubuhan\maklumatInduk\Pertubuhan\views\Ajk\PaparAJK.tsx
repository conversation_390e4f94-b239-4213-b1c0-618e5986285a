import { Box, Grid, Theme, Typography, useMediaQuery } from "@mui/material";
import { useLocation, useNavigate } from "react-router-dom";
import { t } from "i18next";
import { ButtonPrimary } from "../../../../../../../components/button";
import {
  capitalizeWords,
  CitizenshipStatus,
  formatDate,
  getLocalStorage,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
  OrganisationPositions,
} from "@/helpers";
import Input from "@/components/input/Input";
import { gelaranList } from "@/pages/pertubuhan/pengurusan-pertubuhan/senarai-ajk/constanta";

const PaparAJK = () => {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();

  const location = useLocation();
  const committee = location.state?.committee;
  const addressData = location.state?.addressData;
  const societyDetailData = location.state?.societyDetailData;

  const occupationList = getLocalStorage("occupation_list", null);

  return (
    <>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              px: 2,
              py: 4,
            }}
          >
            <Typography
              fontWeight="500 !important"
              fontSize="14px"
              lineHeight="21px"
            >
              {societyDetailData?.societyName ?? "-"} <br />
              {societyDetailData?.societyNo
                ? societyDetailData?.societyNo
                : societyDetailData?.applicationNo ?? "-"}
            </Typography>
          </Box>
        </Box>
      </Box>
      <Box>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("pengerusi")}
            </Typography>
            <Input
              label={t("position")}
              value={Number(committee.designationCode)}
              options={OrganisationPositions.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
          </Box>

          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("chairmanPersonalInfo")}
            </Typography>
            <Input
              label={t("idType")}
              value={committee.identificationType?.toString()}
              options={IdTypes.map((item) => ({
                value: item.value,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("idNumber")}
              value={committee.identificationNo ?? "-"}
              disabled
            />
            <Input
              label={t("title")}
              value={committee.otherPosition?.toString()}
              options={gelaranList.map((item) => ({
                value: item.value,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("fullName")}
              value={committee.name ?? "-"}
              disabled
            />
            <Input
              label={t("gender")}
              value={committee.gender}
              options={ListGender.map((item) => ({
                value: item.value,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("citizenship")}
              value={Number(committee.nationalityStatus)}
              options={CitizenshipStatus.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("dateOfBirth")}
              value={formatDate(committee.dateOfBirth) ?? "-"}
              type="date"
              disabled
            />
            <Input
              label={t("placeOfBirth")}
              value={committee.placeOfBirth ?? "-"}
              disabled
            />
            <Input
              label={t("occupation")}
              value={committee.jobCode}
              options={occupationList}
              type="select"
              disabled
            />
            <Input
              label={t("residentialAddress")}
              value={committee.residentialAddress ?? "-"}
              disabled
            />
            <Input
              label={t("negeri")}
              value={Number(committee.residentialStateCode)}
              options={addressData.map((item: any) => ({
                value: item.id,
                label: t(item.name),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("daerah")}
              value={Number(committee.residentialDistrictCode)}
              options={addressData.map((item: any) => ({
                value: item.id,
                label: t(item.name),
              }))}
              type="select"
              disabled
            />
            <Input
              label={t("poskod")}
              value={committee.residentialPostcode ?? "-"}
              disabled
            />
            <Input label={t("emel")} value={committee.email ?? "-"} disabled />
            <Input
              label={t("phoneNumber")}
              value={committee.phoneNumber ?? "-"}
              disabled
            />
            <Input
              label={t("nomborTelefonRumah")}
              value={committee.telephoneNumber ?? "-"}
              disabled
            />
            <Input
              label={t("nomborTelefonPejabat")}
              value={committee.telephoneNumber ?? "-"}
              disabled
            />
          </Box>

          <Box
            sx={{
              borderRadius: "10px",
              padding: "41px 25px 25px",
              border: "0.5px solid #DADADA",
              marginBottom: "13px",
            }}
          >
            <Typography
              fontSize="14px"
              color="var(--primary-color)"
              fontWeight="500 !important"
              marginBottom="20px"
            >
              {t("employerInfo")}
            </Typography>

            <Input
              label={t("employerName")}
              value={committee.employerName ?? "-"}
              disabled
            />
            <Input
              label={t("employerAddress")}
              value={committee.employerAddress ?? "-"}
              disabled
            />
            <Input
              label={t("state")}
              value={committee.employerStateCode ?? "-"}
              options={
                addressData
                  ?.filter((item: any) => item.pid == MALAYSIA)
                  .map((item: any) => ({
                    label: capitalizeWords(item.name, null, true),
                    value: `${item.id}`,
                  })) ?? []
              }
              type="select"
              disabled
            />
            <Input
              label={t("district")}
              value={committee.employerDistrict ?? "-"}
              type="select"
              options={addressData
                ?.filter(
                  (item: any) => item.pid == committee?.employerStateCode
                )
                .map((item: any) => ({
                  label: capitalizeWords(item.name, null, true),
                  value: `${item.id}`,
                }))}
              disabled
            />
            <Input
              label={t("bandar")}
              value={committee.employerCity ?? "-"}
              disabled
            />
            <Input
              label={t("poskod")}
              value={committee.employerPostcode ?? "-"}
              disabled
            />
          </Box>

          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary type="submit" onClick={() => navigate(-1)}>
              {t("back")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default PaparAJK;
