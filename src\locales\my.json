{"acceptedPeriodDays": "Tempoh Diterima (hari)", "accessOrganizationActivities": "<PERSON><PERSON><PERSON> semua aktiviti pertub<PERSON>an", "accessActivities": "<PERSON><PERSON><PERSON> semua aktiviti", "accessOrganizationReport": "<PERSON><PERSON><PERSON> semua lap<PERSON> per<PERSON>", "accountNumber": "<PERSON><PERSON><PERSON>", "activity": "Aktiviti", "accountsReceivable": "<PERSON><PERSON><PERSON>", "accruedTaxes": "<PERSON><PERSON><PERSON>", "addCawangan": "Tambah Cawangan", "activityFundraisingExpenses": "2.2 <PERSON><PERSON><PERSON><PERSON><PERSON> aktiv<PERSON>/ menjana dana", "activityOrganizationExpenses": "Perbelanjaan aktiviti pertubuhan", "administrativeCosts": "2.3 Kos Pentadbiran", "entertainment": "<PERSON><PERSON><PERSON>", "uniformClothes": "<PERSON><PERSON><PERSON> se<PERSON>", "visitTourFamilyDay": "Lawatan/Pelancongan/<PERSON>", "addContributionFromAbroad": "Tambah sumbangan dari luar negara", "addContributionToAbroad": "Tambah sumbangan ke luar negara", "addDocument": "Tambah Dokumen", "addSupportingDocument": "<PERSON><PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "adakahAndaPastiUntukMenghantarPermohonanIni": "<PERSON><PERSON>h anda pasti untuk menghantar permohonan ini?", "adviser": "<PERSON><PERSON><PERSON>", "age": "<PERSON><PERSON>", "agreementAcceptance": "<PERSON><PERSON><PERSON>", "agreementText": "<PERSON>lah dengan ini saya mengesahkan bahawa segala maklumat yang diberikan adalah benar. Jika pihak Jabatan Pendaftaran Pertubuhan Malaysia mendapati berlaku penipuan dan kepalsuan dalam keterangan dokumen yang telah saya berikan di atas, maka pihak Jabatan Pendaftaran Pertubuhan Malaysia adalah dengan ini berhak untuk menolak permohonan saya dan jika disabitkan bersalah, saya boleh dikenakan denda tidak melebihi denda RM 2000 mengikut seksyen 54 A, AKTA PERTUBUHAN 1966", "agreementText2": "<PERSON>li jawatan kuasa dalam tempoh memegang jawatanya adalah bertang<PERSON> ke atas semua laporan dan maklumat semasa memegang jawatan.", "ahliJawatanKuasa": "<PERSON><PERSON>", "ajkCompletionNote": "*Sila lengkapkan maklumat AJK bagi bilangan 1-7 (WAJIB)", "ajkCount": "Bilangan ahli jaw<PERSON>: {{count}}", "ajkCountNonCitizen": "Bilangan AJK Bukan Warganegara: {{count}}", "ajkEligibilityCheck": "Semakan Kelayakan AJK", "ajkEligibilityVerification": "Pengesahan Kelayakan AJK", "ajkInfoMessage": "Sila pastikan bilangan Ahli Jawatankuasa Biasa mengikut bilangan dalam perlembagaan. Untuk ahli jawatank<PERSON>sa bukan wargan<PERSON>, sila klik.", "ajkInformation": "Maklumat AJK", "ajkList": "Sen<PERSON>i <PERSON>", "ajkName": "Nama AJK", "alamatPertubuhan": "<PERSON><PERSON><PERSON>", "alamatPlaceholder": "<PERSON><PERSON><PERSON><PERSON> alamat tempat mesyuarat", "alamatTempatMesyuarat": "<PERSON><PERSON>t Tempat Mesyuarat", "alamatTempatUrusan": "<PERSON>amat <PERSON> U<PERSON>", "cadanganDanMaklumBalas": "Cadangan dan <PERSON>", "allowancesSalariesWages": "Elaun, gaji, upah", "allowFinancialInfoDisplay": "<PERSON><PERSON> dengan ini saya membenarkan maklumat kewangan ini dipaparkan untuk makluman awam.", "alreadyRegistered": "Sudah berdaftar?", "allStates": "<PERSON><PERSON><PERSON>", "annual": "<PERSON><PERSON><PERSON>", "annualMeetingInformation": "<PERSON><PERSON><PERSON><PERSON>", "annualMeetingSelectionNote": "<PERSON>la pilih tarikh pembentangan penyata tahunan pada dropdown di bawah jika terdapat mesyuarat agung yang diadakan pada tahun ini. <PERSON><PERSON> bertanda * adalah mandatori di sini.", "annualStatement": "<PERSON><PERSON><PERSON>", "annualStatementFrom": "<PERSON><PERSON><PERSON>", "annualStatementTo": "<PERSON><PERSON><PERSON>", "appointmentDate": "<PERSON><PERSON><PERSON>", "approvedNumberOfDays": "<PERSON><PERSON> <PERSON>", "areYouSureContinueLiabilityRestriction": "<PERSON><PERSON><PERSON><PERSON> anda pasti untuk meneruskan $t(liabilityRestriction) terhadap {{- name}}", "areYouSureContinueWhitelisting": "<PERSON><PERSON><PERSON><PERSON> anda pasti untuk meneruskan $t(whitelisting) terhadap {{- name}}", "areYouSureFlowManagementTaskToAnotherCommittee_PENYATAAN_TAHUNAN": "<PERSON><PERSON><PERSON> anda pasti untuk mengalir tugas Pen<PERSON>ta Ta<PERSON> kepada {{- name}}", "areYouSureFlowManagementTaskToAnotherCommittee_PERLEMBAGAAN": "<PERSON><PERSON><PERSON> anda pasti untuk mengalir tugas Pengurusan Perlembagaan kepada {{- name}}", "areYouSureFlowManagementTaskToAnotherCommittee_PENGURUSAN_AJK": "<PERSON><PERSON><PERSON> anda pasti untuk mengalir tugas Pengurusan AJK kepada {{- name}}", "areYouSureFlowManagementTaskToAnotherCommittee_PENGURUSAN_MESYUARAT": "<PERSON><PERSON><PERSON> anda pasti untuk mengalir tugas Pengurusan Mesyuarat kepada {{- name}}", "areYouSureDeleteTrusteeRecord": "<PERSON><PERSON><PERSON> anda pasti untuk memadam rekod pemegang amanah ini?", "areYouSureYouWantToDeleteThisDraftComplaint": "<PERSON><PERSON> {{- title}}", "areYouSureYouWantToResetTheInformationOnThisPage": "<PERSON><PERSON><PERSON>a Pasti Ingin Set <PERSON>?", "areYouSureYouWantToSubmitThisComplaint": "<PERSON><PERSON>h Anda pasti untuk hantar aduan ini?", "asistantSecretary": "<PERSON><PERSON><PERSON>", "asistantTreasurer": "Penolong <PERSON>", "assets": "<PERSON><PERSON>", "assetsBuildingsPlantAndEquipment": "Bangunan, loji, dan peralatan", "assetsAndLiabilities": "<PERSON><PERSON> dan <PERSON>", "assetsLiabilitiesInfo": "Maklumat ini harus diperoleh daripada Lembaran Imbangan Per<PERSON>uhan yang telah disahkan.", "assignAnOfficer": "Tugaskan Pegawai (RO/IO)", "associateMember": "<PERSON><PERSON>", "associateMemberFirstOption": "Terbuka kepada mereka yang tidak memenuhi kriteria untuk Ahli Biasa. Tidak boleh mengundi dan diundi.", "associationIncome": "Pendapatan persatuan", "associationIncomeStatement": "Perdapatan persatuan (RM)", "auditType": "<PERSON><PERSON>", "auditor": "<PERSON><PERSON><PERSON><PERSON>", "auditorInfo": "Maklumat <PERSON>", "auditorInformation": "Maklumat <PERSON>", "auditorList": "<PERSON><PERSON><PERSON>", "auditorName": "<PERSON><PERSON>", "auditorType": "<PERSON><PERSON>", "back": "Kembali", "ban": "Larangan", "bandar": "Bandar", "bandarPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama bandar", "bankAccountInfoMandatory": "Maklumat akaun bank pertubuhan adalah wajib. Sila masukkan maklumat akaun bank yang sah.", "bankCharges": "Caj bank", "bankName": "Nama bank", "bayaranOnlinePertubuhan": "<PERSON><PERSON>", "bonus": "Bonus", "branchAddress": "<PERSON><PERSON><PERSON>", "branchBusinessAddress": "$t(alamatTempatUrusan1) $t(cawangan)", "branchComplaint": "$t(complaint) $t(cawangan)", "branchCount": "Bilangan cawangan", "branchLiquidationInformation": "Maklumat Pembubaran Cawangan", "branchLevel": "<PERSON><PERSON>", "branchNumber": "No. Cawangan", "branchOrganization": "<PERSON><PERSON><PERSON><PERSON>", "branchPublicOfficerInformations": "$t(publicOfficialsInformation) $t(cawangan)", "branchPropertyOfficerInformations": "$t(propertyOfficerInformation) $t(cawangan)", "branchState": "<PERSON><PERSON><PERSON>", "buildings": "Bangunan", "businessAddress": "<PERSON><PERSON><PERSON>", "businessAddressLabel": "<PERSON><PERSON><PERSON> Menyurat", "businessOfficePhoneNumber": "Nombor Telefon Pejabat Urusan", "butiranAlasanPermohonanRayuan": "Butiran/Alasan <PERSON>", "calendar": "Takwim", "calendarAllActivities": "Semua Aktiviti", "calendarActionAddToGoogleCalendar": "Tambah ke Kalendar Google", "calendarActivityCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "calendarActivityDescription": "Deskripsi Aktiviti", "calendarActivityDetails": "Butiran Aktiviti", "calendarActivityLists": "Senarai Aktiviti", "calendarActivityObjectives": "Objektif Activiti", "calendarNextActivities": "Aktiviti seterusnya", "calendarThisMonth": "<PERSON><PERSON><PERSON> ini", "calendarThisYear": "<PERSON><PERSON> ini", "calendarTitle": "Takwim Aktiviti JPPM", "calendarSeeTheFullActivities": "Lihat Aktiviti Sepenuhnya", "cancellation": "Pembatalan", "cashInBank": "Tunai Di Bank", "cashInHand": "Tunai <PERSON>", "cawanganMigrasi": "Cawanga<PERSON>", "cawanganMigrasiInfo": "<PERSON>la klik ikon \"pensil\" untuk mengemaskini nama dan alamat <PERSON> semasa (tahun 2015). Setiausaha <PERSON>awangan bertanggungjawab untuk menghantar Penyata Tahunan <PERSON>awangan 2014.", "chairman": "<PERSON><PERSON><PERSON><PERSON>", "branchChairman": "Pen<PERSON><PERSON><PERSON>", "charitySale": "Ju<PERSON> amal", "charitySaleIncome": "Ju<PERSON> amal", "checkBox": "<PERSON><PERSON>kan maklumat dalam fasal di atas", "checkTheConstitution": "Se<PERSON>k <PERSON>", "citizenship": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "citizen": "Warganegara", "city": "Bandar", "civilServantRegistration": "Pendaftaran Pegawai Awam", "clause": "Fasal", "clauseDetail": "<PERSON><PERSON><PERSON>", "clauseContent": "<PERSON><PERSON><PERSON><PERSON>", "clauseList": "<PERSON><PERSON><PERSON>", "clauseName": "<PERSON><PERSON> fasal", "clickHere": "di sini", "codeResent": "<PERSON><PERSON> <PERSON><PERSON> semula", "committeeAndAuditorInformation": "Maklumat J<PERSON> dan <PERSON>", "committeeList": "Sen<PERSON>i <PERSON>", "committeMember": "<PERSON><PERSON>", "Committee updated successfully.": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>", "complainantDetails": "<PERSON><PERSON><PERSON>", "complaint": "<PERSON><PERSON>", "complaintAction": "Tindakan $t(complaint)", "complaintDecision": "Keputusan $t(complaint)", "complaintDetails": "Butir $t(complaint)", "complaintHistory": "Sejarah $t(complaint)", "complaintInformation": "$t(maklumat) $t(complaint)", "complaintList": "$t(list) $t(complaint)", "complaintOfOrganizationalMisconduct": "$t(complaint) Salah <PERSON>", "complaintSearch": "Carian $t(complaint)", "complaintReferenceNumber": "$t(referenceNumber) $t(complaint)", "complaintTopic": "Topik $t(complaint)", "complainantsName": "<PERSON><PERSON>", "complaintsBranch": "Cawangan $t(complaint)", "complete": "Lengka<PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON> semula kata laluan", "confirmPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> semula kata laluan baru", "constitution": "Perlembagaan", "constitutionalAmendment": "<PERSON><PERSON><PERSON>", "constitutionDownload": "$t(download) $t(constitution)", "constitutionalInterpretation": "Tafsiran <PERSON>", "constitutionType": "<PERSON><PERSON>", "continueLiabilityRestrictionSuccess": "{{- name}} telah disenarai hitam dan notis 49 telah berjaya dijana!", "contribution": "Sumbangan", "contributionFromAbroad": "Sumbangan dari luar negara", "contributionOption": "Pertubuhan dibenarkan untuk menerima sumbangan dalam bentuk wang dan barangan. Sumbangan berbentuk wang hendaklah diisytiharkan dengan jelas dan tepat dalam Pen<PERSON>ta <PERSON>.", "contributionProvider": "Pemberi sumbangan", "contributionsFromAbroad": "Sumbangan Dari/Ke Luar Negara", "contributionToAbroad": "Sumbangan ke luar negara", "contributionToAbroadInformation": "Maklumat sumbangan ke luar negara", "counter": "<PERSON><PERSON><PERSON>", "country": "Negara", "countryOfOrigin": "Negara asal sumbangan", "countryOfOriginRecipient": "Negara asal sumbangan", "countryOfOriginRecipient2": "Negara asal penerima", "customerFeedback": "<PERSON><PERSON><PERSON> balas", "currentDate": "<PERSON><PERSON><PERSON>", "currentLiabilities": "Liabiliti Semasa", "currentInvestments": "<PERSON><PERSON><PERSON><PERSON>", "createAComplaint": "<PERSON><PERSON><PERSON>", "creditors": "Pemiutang", "downloadTemplate": "Muat turun templat minit untuk rujukan", "daerah": "<PERSON><PERSON><PERSON>", "daftarCawangan": "Daftar <PERSON>ngan", "dalamProsesPermohonanLanjutanMasa": "<PERSON><PERSON> proses permo<PERSON>an LANJUTAN MASA", "dateApplicationRejected": "<PERSON><PERSON><PERSON>", "dateApplied": "<PERSON><PERSON><PERSON>", "dateFormatWithPrefix": "{{prefix}} {{dateFormat}}", "dateOfBirth": "<PERSON><PERSON><PERSON>", "dateOfBirthCapitalizedOnlyFirstLetter": "<PERSON><PERSON><PERSON> la<PERSON>", "dateOfCommencementOfTheOffence": "<PERSON><PERSON><PERSON>", "dateOfComplaint": "<PERSON><PERSON><PERSON>", "dateOfOccurrence": "<PERSON><PERSON><PERSON>", "datePrefixOn": "[pada]", "deathCharity": "<PERSON><PERSON><PERSON> kema<PERSON>", "decisionMethod": "<PERSON><PERSON><PERSON>", "deleteConfirmationMessage": "<PERSON><PERSON>h anda pasti untuk padam permohonan ini?", "deleteDocument": "Padam Dokumen", "deleteInfo": "Padam maklumat", "deleteOrgInfo": "Padam Maklum<PERSON>", "deleteOrganizationInfo": "Padam Maklum<PERSON>", "deletionWarning": "Permohonan pendaftaran pertubuhan anda akan dipadam selepas 30 hari dari tarikh mula mohon. <PERSON>la maklumat permohonan anda akan {{ attr }} jika melebihi tempoh permohonan pendaftaran.", "descent": "Keturunan", "dinnerEvent": "<PERSON><PERSON>a makan malam", "dinnerEventIncome": "<PERSON><PERSON> makan malam", "disahkanOlehPengerusi": "<PERSON><PERSON><PERSON><PERSON>", "disediakanOlehSetiausaha": "Disedia<PERSON>", "displayFormattedDateTimeComplete": "{{date, dddd, D MMMM YYYY h:mm A}}", "district": "<PERSON><PERSON><PERSON>", "dividend": "Dividen", "dividendIncome": "Pendapatan dividen", "document": "Dokumen", "documentName": "<PERSON><PERSON>", "documentRemarks": "Catatan Dokumen", "documentType": "Tipe @t(document)", "dokumenPermohonanRayuan": "<PERSON><PERSON><PERSON>", "donation": "Sumbangan", "donationIncome": "Der<PERSON>", "draft": "Draf", "draftComplaintSuccessfullyDeleted": "<PERSON><PERSON> {{- title}} <PERSON><PERSON><PERSON><PERSON>!", "durationInMalaysia": "Tempoh berada di Malaysia", "economicActivity": "Kegiatan ekonomi", "economicActivityOption": "<PERSON><PERSON><PERSON>an boleh menjalankan kegiatan ekonomi seperti menjual, membe<PERSON>, me<PERSON><PERSON>, menye<PERSON>, memiliki harta alih dan tidak alih serta lain-lain kegiatan ekonomi. Se<PERSON>a wang dan keuntungan yang diperoleh hasil daripada kegiatan ekonomi hendaklah disalurkan kembali kepada Pertubuhan bagi mencapai matlamat Pertubuhan dan tidak boleh digunakan untuk membayar faedah, keuntungan atau bonus kepada mana-mana ahli <PERSON>. Walau bagaimanapun, peruntukan ini tidak menghalang sebarang pembayaran gaji atau perbelanjaan pentadbiran atau kedua-duanya kepada mana-mana ahli atau pekerja Pertubuhan. ", "electionOfAJK": "Pemilihan AJK dibuat dalam Mesyuarat Agung", "electionPeriod": "Tempoh pelantikan jawatankuasa", "eligibleVoterCount": "<PERSON><PERSON><PERSON><PERSON> ahli yang layak mengundi", "email": "E-mel", "emailWithoutDash": "<PERSON><PERSON>", "emailPlaceholder": "Masukkan e-mel anda", "emailRequired": "<PERSON>-mel dip<PERSON><PERSON>an", "emel": "E-mel", "emelPenerima": "E-mel Penerima", "employerAddress": "<PERSON><PERSON><PERSON>", "employerInfo": "Mak<PERSON><PERSON>", "employerInfoNote": "* <PERSON>i jika berkenaan", "employerName": "<PERSON><PERSON>", "enterAjkName": "Ma<PERSON>kkan nama AJK", "enterAuditorName": "<PERSON><PERSON><PERSON><PERSON>", "endTime": "<PERSON><PERSON>", "enforcement": "Penguatkuasaan", "enforcement_management": "Pengurusan $t(enforcement)", "enterMemberName": "<PERSON><PERSON><PERSON><PERSON> nama ahli", "enterOrgName": "<PERSON>la masukkan nama pertubuhan dengan betul.", "enterOrganizationName": "<PERSON><PERSON><PERSON><PERSON> nama per<PERSON>an", "enterPropertyOfficerName": "<PERSON><PERSON><PERSON>n nama pegawai harta", "enterPublicOfficerName": "<PERSON><PERSON><PERSON><PERSON> nama pegawai awam", "enterTrusteeName": "<PERSON><PERSON><PERSON><PERSON> nama pemegang amanah", "entranceFee": "<PERSON><PERSON>", "entranceFeeIncome": "<PERSON><PERSON>", "epfSocso": "KWSP/PERKESO", "eRoses": "eRoses", "eROSES": "eROSES", "establishmentMeeting": "<PERSON><PERSON><PERSON><PERSON>", "example": "<PERSON><PERSON><PERSON>", "expenditure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (RM)", "faxNumber": "No. faks", "facebook": "Facebook", "feeAmount": "<PERSON>aun yuran", "feeManagement": "<PERSON><PERSON><PERSON><PERSON>", "feePaymentPeriod": "Tempoh bayaran yuran", "feePaymentStipulation": "Ketetapan bayaran yuran", "female": "Perempuan", "filterButton": "<PERSON><PERSON>", "financialMangement": "<PERSON><PERSON><PERSON><PERSON>", "financialResources": "<PERSON><PERSON>", "financialYearStart": "<PERSON><PERSON><PERSON><PERSON> tahun kewangan", "financialYearStartsOn": "<PERSON><PERSON>", "firstGoals": "1. <matlamat 1>", "fixedAssets": "<PERSON><PERSON>", "fixedDepositInterest": "Faedah deposit tetap", "fixedDepositInterestIncome": "<PERSON>ae<PERSON> dari simpanan tetap", "fixedDeposits": "Deposit Tetap", "flagEmblemBadges": "<PERSON><PERSON>, lamba<PERSON>, dan lencana", "forbidden": "<PERSON><PERSON><PERSON>", "forbiddenMessage": "Anda tidak mempunyai kebenaran untuk mengakses sumber ini. <PERSON>la hubungi pentadbir anda jika anda percaya ini adalah ralat.", "forgotPassword": "Lupa Kata Laluan?", "forgotPasswordTitle": "Lu<PERSON> Kat<PERSON>", "fullName": "<PERSON><PERSON>", "fullNameCapitalizedOnlyFirstLetter": "<PERSON><PERSON>", "fullNamePlaceholder": "<PERSON>a penuh seperti dalam kad pengenalan", "fullNameRequired": "<PERSON><PERSON> pen<PERSON> diperlukan", "fundraisingActivitiesIncome": "1.2 Hasil Aktiviti Menjana Dana", "fundraisingActivitiesIncomeSection": "1.2 Hasil Aktiviti Menjana Dana", "fundraisingExpenses": "Perbelanjaan aktiviti kutipan derma", "fundraisingIncome": "<PERSON><PERSON><PERSON> dana", "furniture": "<PERSON><PERSON><PERSON>", "gazette": "<PERSON><PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON>", "generalCharity": "<PERSON><PERSON><PERSON>", "generalInformation": "Maklumat Am", "generalMeeting": "Me<PERSON><PERSON><PERSON>", "giftsPresents": "<PERSON><PERSON><PERSON><PERSON>/ hadiah", "github": "GitHub", "goals": "Mat<PERSON><PERSON>", "goalsFormat": "Format matlamat:", "goHome": "<PERSON>", "governmentAgency": "<PERSON><PERSON><PERSON> k<PERSON>", "governmentAgencyGrant": "<PERSON><PERSON> <PERSON><PERSON>i kera<PERSON>", "grant": "Geran", "grants": "1.4 Geran", "grantsSection": "1.4 Geran", "halHalLain": "<PERSON><PERSON><PERSON>", "hantar": "Hantar", "hantarPermohonan": "<PERSON><PERSON>", "historyOfWhitening": "<PERSON><PERSON><PERSON>", "homeNumber": "Nombor Telefon Rumah", "honoraryMember": "<PERSON><PERSON>", "honoraryMemberFirstOption": "Terdiri daripada mereka yang berjasa kepada masyarakat dan boleh memberi sumbangan kepada Pertub<PERSON>an. <PERSON>reka ditentukan oleh jawatankuasa dan terkecuali dari pembayaran yuran", "idNumber": "Nombor Pengenalan Diri", "idNumberCapitalizedOnlyFirstLetter": "Nombor Pengenalan Diri", "idNumberPlaceholder": "No. Pengenalan Diri", "idPlaceholder": "No ID Berdaftar", "idType": "<PERSON><PERSON>", "idTypeCapitalizedOnlyFirstLetter": "<PERSON><PERSON> diri", "importanceOfPosition": "Terangkan secara ringkas kepentingan bukan warganegara untuk memegang jawatan", "inactiveTaskFlowMemberList": "Senarai <PERSON> Aliran Tugas Tidak Aktif", "incidentDetails": "<PERSON><PERSON><PERSON>", "inComplete": "Tidak lengkap", "incomeStatement": "Penyata Pendapatan", "incomeStatementInfo": "Maklumat ini harus diperoleh daripada Penyata Pendapatan Pertubuhan yang telah disahkan dalam Mesyuarat Agung Tahunan atau jika tiada mesyuarat agung sedemikian diadakan pada tahun itu, ia perlu disahkan oleh Ju<PERSON> pertubuhan.", "incomeStatementInfo2": "<PERSON><PERSON><PERSON> yang perlu diisi hendaklah dibundarkan kepada dua tempat perpuluhan, contohnya: RM 500.59 ~ RM 500.60", "individual": "Individu", "individualGrant": "Geran individu", "individualReview": "Semakan $t(individual)", "inputValidationErrorDateAboveMax": "{{label}} tidak boleh melebihi {{value}}", "inputValidationErrorDateBelowMin": "{{label}} tidak boleh kurang dari {{value}}", "inputValidationErrorIdentityCardNumberAndNameAreNotInTheRecords": "Ralat : <PERSON><PERSON><PERSON> dan Nama tiada dalam rekod mana-mana per<PERSON>an", "inputValidationErrorFileSizeExceedsLimit": "Saiz fail melebihi had 25MB. Sila muat naik fail yang lebih kecil.", "inputValidationErrorFileSizeExceedsLimitDynamic": "Saiz fail me<PERSON><PERSON><PERSON> had {{limit}}. <PERSON>la muat naik fail yang lebih kecil.", "inputValidationErrorNumberMustContainDigit": "{{label}} mesti men<PERSON>du<PERSON>i {{digit}} digit.", "inputValidationErrorPleaseFillTheRequiredFields": "<PERSON>la isi medan yang diperlukan.", "inputValidationErrorStringExceedsLimitWord": "{{label}} tidak boleh melebihi {{value}} patah perkataan", "inputValidationErrorTotalAttendeesBelowMin": "<PERSON><PERSON><PERSON><PERSON> mestilah mempunyai sekurang-kurangnya {{value}} ahli yang hadir.", "inquiry": "<PERSON><PERSON><PERSON>", "instagram": "Instagram", "instructionLetterReferenceNumber": "$t(letterReferenceNumber) Arahan/Sokongan", "inspection": "Pemeriks<PERSON>", "insurance": "Insuran", "intangibleAssets": "Aset Tidak Nyata", "integrationJPNActive": "Integrasi JPN telah dibuka dan semakan yang terlibat dengan JPN berfungsi.", "integrationJPNInactive": "Integrasi JPN telah ditutup dan semakan yang terlibat dengan JPN tidak berfungsi.", "integrationEPICActive": "Integrasi EPIC telah dibuka dan Cara Pembayaran Online akan dibuka.", "integrationEPICInactive": "Integrasi EPIC telah ditutup dan <PERSON> Pembayaran Online akan ditutup.", "investigation": "Siasatan", "investmentAssets": "<PERSON><PERSON>", "investmentExpenses": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "investmentIncome": "1.3 Pendapatan Pelaburan", "investmentIncomeSection": "1.3 Pendapatan Pelaburan", "inventory": "Inventori", "isiMaklumatMesyuarat": "<PERSON><PERSON>", "janaMinit": "<PERSON>", "janaKodQR": "<PERSON>", "jawatanDalamPertubuhan": "<PERSON><PERSON><PERSON>", "jawatanKuasaNotes": "*Untuk ahli jawatank<PERSON>sa yang TIDAK BEKERJA/BERSARA, maklumat majikan tidak perlu diisi", "jemputanMesyuarat": "Jemputa<PERSON>", "jenisMesyuarat": "<PERSON><PERSON>", "jenisPlatform": "Jenis Platform", "jenisPlatformPlaceholder": "Masukkan jenis platform", "jenisRayuan": "<PERSON><PERSON>", "jppmUserCategoryCreatedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> telah berjaya ditambah bagi {{name}}", "jppmUserCategoryUpdatedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> telah berjaya dike<PERSON> bagi {{name}}", "jumlahKehadiran": "<PERSON><PERSON><PERSON>", "jumlahKehadiranAhliMesyuarat": "<PERSON><PERSON><PERSON> (Orang)", "jumlahKehadiranPlaceholder": "<PERSON><PERSON><PERSON><PERSON> jumlah keh<PERSON>", "kaedahMesyuarat": "<PERSON><PERSON><PERSON>", "kemaskiniMaklumatCawangan": "Kemaskini Maklumat Cawangan", "kodQR": "Kod QR", "land": "<PERSON><PERSON>", "lanjutMasa": "<PERSON><PERSON><PERSON><PERSON>", "latestConstitution": "Perlembagaan Terkini", "letterReferenceNumber": "$t(referenceNumber) Surat", "liabilities": "Liabiliti", "liabilityRestriction": "Sekatan $t(liabilities)", "liabilityRestrictionCreate": "Cipta $t(liabilityRestriction)", "liabilityRestrictionInformation": "Informasi $t(liabilityRestriction)", "lihatSenaraiPenuh": "<PERSON><PERSON>", "lifetimeFee": "<PERSON><PERSON>up", "lifetimeMember": "<PERSON><PERSON>", "lifetimeMemberFirstOption": "<PERSON><PERSON><PERSON> kepada <PERSON>li <PERSON>a yang membayar yuran sekali gus.", "liquidation": "Pem<PERSON><PERSON><PERSON>", "liquidationInformation": "Maklumat Pembubaran", "liquidationWarning": {"main": "Sila rujuk Fasal Pembubaran dalam Perlembagaan Pertubuhan anda. Sekiranya tiada peruntukan pembubaran dalam Perlembagaan Pertubuhan anda, sila patuhi peruntukan seperti berikut:", "point1": "i-Pembubaran dipersetujui oleh tidak kurang daripada 3/5 daripada jumlah ahli berdaftar atau 3/4 ahli bagi Pertubuhan F<PERSON>.", "point2": "ii-<PERSON><PERSON> hutang dan tanggungan Pertubuhan yang sah mengikut Perlembagaan hendaklah dijelaskan dan baki wang yang tinggal hendaklah diselesaikan mengikut cara yang dipersetujui dalam Mesyuarat Agung berkenaan.", "point3": "Sila isi maklumat mesyuarat pembubaran jika <PERSON> tiada dalam pilihan atau klik <1>disini</1> bagi kemasukan maklumat mesyuarat terlibat.", "point4": "<PERSON> penye<PERSON>aian pada baki aset hendaklah selaras dengan keputusan mesyuarat agung pembubaran."}, "list": "<PERSON><PERSON><PERSON>", "listOfBannedNames": "<PERSON><PERSON><PERSON> nama la<PERSON>an", "listOfBranchCivilServants": "Sen<PERSON>i <PERSON>", "listOfBranchCivilServantsAwaitingApproval": "$t(listOfBranchCivilServants) ($t(statusAwaitingApproval))", "listOfBranchPropertyOfficer": "Senarai <PERSON> Hart<PERSON> Cawangan", "listOfBranchPropertyOfficerAwaitingApproval": "$t(listOfBranchPropertyOfficer) ($t(statusAwaitingApproval))", "listOfContributionsFromAbroad": "<PERSON><PERSON><PERSON> sumbangan dari luar negara", "listOfLiabilityRestriction": "Senarai $t(liabilityRestriction)", "loans": "<PERSON><PERSON><PERSON>", "localAuthority": "<PERSON><PERSON> be<PERSON> tempatan", "login": "Log Ma<PERSON>k", "loginButton": "Log masuk", "loginHere": "LOG MASUK", "loginTitle": "Log Ma<PERSON>k", "logout": "<PERSON><PERSON>", "longTermDebt": "Hutang Jang<PERSON> Panjang", "longTermLiabilities": "Liabiliti Jangka Panjang", "longTermLoans": "<PERSON><PERSON><PERSON>", "meetingManagement": "<PERSON><PERSON><PERSON><PERSON>", "machineryEquipment": "Mesin/Jentera", "mailingAddress": "<PERSON><PERSON><PERSON> Menyurat", "maintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maklumatCawangan": "Maklumat Cawangan", "maklumatMesyuarat": "Maklumat Mesyuarat", "maklumatMesyuaratPembubaranCawangan": "$t(maklumatMesyuarat) $t(liquidation) $t(cawangan)", "maklumatMesyuaratPenubuhan": "Maklumat Mesyuarat", "maklumatPermohonanRayuanPertubuhan": "Maklumat permohonan rayuan pet<PERSON>an", "male": "<PERSON><PERSON><PERSON>", "mandatoryInfo": "<PERSON><PERSON>a medan bertanda * adalah mandatory", "masa": "<PERSON><PERSON>", "masaLanjutInfo": "<PERSON><PERSON><PERSON><PERSON> lanjutan masa hanya dibenarkan sekali sahaja.", "masaPlaceholder": "<PERSON><PERSON><PERSON> masa", "matter": "Perkara", "mattersDiscussedPlaceholder": "<PERSON><PERSON> ma<PERSON> p<PERSON><PERSON> Aktiv<PERSON>, <PERSON><PERSON><PERSON>, Keputusan Perlantikan AJK Baharu, Cadangan Pindaan Perlembagaan jika ada", "maximumPeriod": "<PERSON><PERSON><PERSON><PERSON> tempoh tunggakan yuran yang di<PERSON>an", "meeting": "Mesyuarat", "meetingDate": "<PERSON><PERSON><PERSON>", "meetingInformationTheNumberOfMembersEntitledToVote": "<PERSON><PERSON><PERSON><PERSON> ahli yang berhak mengundi", "meetingInformationNumberOfMembersAttendingTheMeeting": "Bilangan ahli yang hadir mesyuarat", "meetingInformationTheNumberOfMembersWhoAgreeToTheDissolution": "Bilangan ahli yang bersetuju dengan pembubaran", "ajkMeetingFrequency": "Kekerapan mesyuarat jawata<PERSON>", "meetingFrequency": "Kekerapan mesyuarat", "memberFeeIncome": "<PERSON><PERSON>", "memberInformation": "<PERSON><PERSON><PERSON><PERSON>", "memberList": "<PERSON><PERSON><PERSON>", "memberName": "<PERSON><PERSON>", "memberNumber": "No. ahli", "memberRegister": "<PERSON><PERSON><PERSON>", "membershipCard": "<PERSON><PERSON>", "membershipFee": "<PERSON><PERSON>", "membershipFeeIncome": "<PERSON><PERSON>", "minimumAge": "<PERSON><PERSON>", "minitMesyuarat": "Muat Naik <PERSON>", "minitMesyuaratViewOnly": "<PERSON><PERSON>", "mobile": "Telefon Bimbit", "mobileNumber": "No. telefon bimbit", "modification": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "muatNaik": "<PERSON><PERSON>", "muatNaikDokumenDisini": "Muat naik dokumen di sini", "muatTurun": "<PERSON>at turun", "myComplaint": "$t(complaint) Saya", "mykad": "MyKad", "namaKodQR": "Nama kod QR", "namaCawangan": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "nameOfThePerpetrator": "$t(name) Pelaku", "tambahCawangan": "Tambah Cawangan", "penambahanCawangan": "<PERSON><PERSON><PERSON><PERSON>", "keteranganCawangan": "Ketarangan Cawangan", "namaPenuh": "<PERSON><PERSON>", "namaPertubuhan": "<PERSON><PERSON>", "nameAbbreviation": "Singkatan Nama", "nameDefinition": "<PERSON><PERSON><PERSON><PERSON>", "nationalOrganizationNote": "• <PERSON><PERSON> ta<PERSON><PERSON>, maklumat AJK mestilah terdiri daripada 7 AJK dari negeri yang berbeza.", "negeri": "<PERSON><PERSON><PERSON>", "newPassword": "<PERSON><PERSON>", "newPasswordPlaceholder": "<PERSON><PERSON>kkan kata laluan baru", "next": "Seterusnya", "no": "Tidak", "noKadPengenalan": "No Kad Pengenalan", "noRegister": "No pendaftaran", "noTelefonBimbit": "No. Telefon Bimbit", "noTelefonPejabat": "No. Telefon Pejabat", "notWhitelisted": "<PERSON><PERSON>", "noAssetsLiabilitiesDeclaration": "Dengan ini saya mengaku bahawa persatuan ini tidak mempunyai sebarang aset dan liabiliti.", "none": "Tiada", "nonCitizen": "Bukan <PERSON>", "nonCitizenAJK": "AJK Bukan Warganegara", "nonCitizenCommittee": "Jawatankuasa bukan warganegara", "nonCitizenCommitteeSuccessfullyCreated": "$t(nonCitizenCommittee) telah dicipta.", "nonCitizenCommitteeSuccessfullyUpdated": "$t(nonCitizenCommittee) telah dikemaskini.", "nonCitizenCommitteeInformation": "$t(maklumat) $t(positionOfAuthority) $t(nonCitizen)", "noticeManagement": "<PERSON><PERSON><PERSON><PERSON>", "notRegistered": "Belum berdaftar?", "number": "Bil", "numberOfMembersAttendingTheMeeting": "$t(bilangan) ahli yang hadir $t(meeting)", "numbers": "Nombor", "organizationEmpowerment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "organizationActivities": "Senarai aktiviti pertubuhan", "occupation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offenseSection": "$t(section) <PERSON><PERSON><PERSON>", "officeEquipmentSupplies": "Kelengkapan <PERSON>/ Peralatan", "officeNumber": "Nombor Pejabat", "officerCount": "Bilangan pegawai", "officerCount2": "Bilangan Pemegang Jawatan: {{count}}", "officerReceivingReport": "Pejabat Penerima Report", "onePage": "<PERSON><PERSON>", "onlinePayment": "Bayaran online pertubuhan", "onlineUpperscoredPayment": "Bayaran ONLINE", "open": "<PERSON><PERSON><PERSON>", "operatingExpenses": "2.1 <PERSON><PERSON><PERSON><PERSON><PERSON>", "operatingIncome": "1.1 Pendapatan Operasi", "operatingIncomeSection": "1.1 Pendapatan Operasi", "orang": "orang", "organization": "<PERSON><PERSON><PERSON>", "organizationActivityPromotion": "Promosi aktiviti pertubuhan", "organizationApplicationDate": "<PERSON><PERSON><PERSON> per<PERSON>", "organizationBankAccountInfo": "Maklumat akaun bank pertubuhan", "organizationCategory": "<PERSON><PERSON><PERSON>", "organizationComplain": "$t(complaint) Per<PERSON><PERSON>an", "organizationContactInfo": "Maklumat Perhubungan Per<PERSON>uhan", "organizationDocumentList": "<PERSON><PERSON><PERSON>", "organizationFaxNumber": "Nombor faks per<PERSON>uhan", "organizationGoals": "<PERSON><PERSON><PERSON>", "organizationInfo": "Maklum<PERSON>", "organizationInformation": "Maklum<PERSON>", "organizationLevel": "<PERSON><PERSON>", "organizationManagement": "<PERSON><PERSON><PERSON><PERSON>", "organizationName": "<PERSON><PERSON>", "organizationNameHelper": "<PERSON><PERSON> henda<PERSON> bermula den<PERSON>, <PERSON><PERSON><PERSON><PERSON>", "organizationNamePlaceholder": "<PERSON><PERSON>an telah wujud", "organizationNumber": "<PERSON><PERSON><PERSON>", "organizationNumberLowerCase": "<PERSON><PERSON><PERSON>", "organizationOnlinePayment": "<PERSON><PERSON>", "organizationPhoneNumber": "Nombor telefon pertubuhan", "organizationRegistration": "Pendaft<PERSON>", "organizationReport": "<PERSON><PERSON><PERSON>", "organizationStatus": "Status Per<PERSON>uhan", "organizationSubCategory": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "organizationTypeInfo": "Perlembagaan hendaklah mengikut template yang disediakan oleh JPPM", "originCountry": "<PERSON>eg<PERSON>al", "otherDocuments": "<PERSON><PERSON><PERSON>", "otherExpenses": "2.4 <PERSON>n-lain per<PERSON><PERSON>", "otherFundraisingIncome": "Lain-lain pendapatan menjana dana", "otherGrants": "Lain-lain geran", "otherInvestmentIncome": "Lain-lain pendapatan pelaburan", "otherMatter": "<PERSON><PERSON> lain", "otherOperatingIncome": "Lain-lain pendapatan <PERSON>i", "otherOrganizationNote": "• <PERSON>gi lain-lain ta<PERSON><PERSON>, tiada syarat yang ditetapkan pada maklumat AJK.", "otherSkillsCriteria": "<PERSON><PERSON><PERSON>", "others": "Lain-lain", "orgDocList": "<PERSON><PERSON><PERSON>", "personalInfo": "Maklumat Peribadi", "padamMaklumatPertubuhan": "Padam Maklum<PERSON>", "paparanCawangan": "<PERSON><PERSON>", "parent": "Induk", "parentComplaint": "$t(complaint) $t(parent)", "parentConstitution": "Perlembagaan Induk", "parliament": "Parlimen", "participationFees": "<PERSON><PERSON>", "password": "<PERSON><PERSON>", "passport": "Passport", "payment": "Pembayaran", "paymentMethod": "<PERSON>", "paymentNote": "NOTA: <PERSON><PERSON> membuat bayaran di kaunter JPPM yang berdekatan jika anda memilih cara pembayaran di kaunter.", "paymentReferenceNumber": "No Rujukan Pem<PERSON>aran", "paymentStatusAlert": "Bagi status bayaran online, status akan dikemaskini dalam masa 24 jam. Jika status bayaran tidak berubah, sila rujuk negeri berdekatan.", "pekerjaan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pembubaranNote": "<PERSON>la pilih tarikh pembentangan pembubaran pada dropdown dibawah. <PERSON><PERSON> bertanda * adalah mandatori disini.", "PEMType": "Jenis <PERSON>", "PEMIINotePopoverText": "Diminta untuk isi catatan berkaitan aduan dan hantar semula PEM II jika terdapat sebarang penerimaan aduan dari cawangan lain", "PEMNoteWithNumber": "Catatan PEM {{number}}", "PEMWithNumber": "PEM {{number}}", "pengurusanCawangan": "Cawangan", "pengurusanIndividu": "<PERSON><PERSON><PERSON><PERSON> individu pertubuhan dan keahlian", "pengurusanPertubuhan": "<PERSON><PERSON><PERSON><PERSON>", "penutup": "<PERSON><PERSON><PERSON>", "perakuanPemohonan": "Perak<PERSON> pemohonan", "periodOfDefendingOneself": "Tempoh membela diri/ meminta penje<PERSON>an dari pemecatan ahli", "periodOfDefendingOneselfTwo": "<PERSON><PERSON><PERSON> membela diri/ meminta penje<PERSON>an dari penggant<PERSON>an/ pelucutan jawata<PERSON><PERSON><PERSON>", "perkaraPerkara": "Perkara Yang <PERSON>cangkan", "permitExpiryDate": "<PERSON><PERSON><PERSON>", "permitNumber": "No. Permit", "permohonanCawanganLuput": "<PERSON><PERSON><PERSON><PERSON>", "permohonanLanjutanMasa": "<PERSON><PERSON><PERSON><PERSON> lanjutan masa", "peringatanHubungiJPPM": "<PERSON>la hubungi JPPM Negeri <NAME_EMAIL> jika rekod cawangan pertubuhan anda tiada dalam senarai ini.", "petaLokasiSuratMenyurat": "Peta lokasi surat menyurat", "phone": "No Tel HP/R/P", "phoneNumber": "Nombor Telefon", "pleaseSaveThisComplaintReferenceNumber": "Sila Simpan Nombor Rujukan Ini. <PERSON><PERSON> Menyemak Status <PERSON>uan Dengan Memasukkan Nombor Rujukan Tersebut", "mobilePhoneNumber": "Nombor Telefon Bimbit", "phoneNumberCapitalizedOnlyFirstLetter": "Nombor telefon", "phoneNumberRequired": "Nombor telefon diperlukan", "photocopy": "Fotokopi", "place": "Tempat", "placeOfBirth": "Tempat Lahir", "placeOfBirthCapitalizedOnlyFirstLetter": "Tempat lahir", "placeOfBusiness": "Tempat urusan", "placeOfBusinessLocationMap": "$t(locationMap) $t(placeOfBusiness)", "pleaseSelectAtLeastOneCommitteeBeforeUpdating": "<PERSON>la pilih sekurang-kurangnya satu AJK sebelum mengemaskini.", "position": "Jawatan", "positionOfAuthority": "Jawatankuasa", "positionOfAuthorityTasks": "Tugas-tugas Jawatankuasa", "poskod": "Poskod", "poskodPlaceholder": "Masukkan poskod", "postcode": "Poskod", "ppmBranchNumber": "$t(noppm) $t(cawangan)", "previous": "<PERSON><PERSON><PERSON>", "private": "<PERSON><PERSON><PERSON>", "privateAgency": "<PERSON><PERSON><PERSON> swasta", "privateAgencyGrant": "<PERSON><PERSON> agensi swasta", "profile": "Profil", "projectStatus": "Status Projek", "propertyOfficer": "Pegawa<PERSON>", "propertyOfficerAndOrPublicOfficer": "$t(propertyOfficer) dan/atau $t(publicOfficer)", "propertyOfficerCreatedSuccessfully": "Permohonan $t(propertyOfficer) <PERSON><PERSON><PERSON><PERSON>", "propertyOfficerUpdatedSuccessfully": "Permohonan $t(propertyOfficer) <PERSON><PERSON><PERSON><PERSON>", "propertyOfficerInformation": "Maklumat Pegawai Harta", "propertyOfficerList": "<PERSON><PERSON><PERSON>", "propertyOfficerName": "<PERSON><PERSON>", "propertyOfficerRegistration": "Pendaftaran $t(propertyOfficer)", "propertyProfit": "Keuntungan harta", "propertyProfitIncome": "Keuntungan harta", "prosecution": "<PERSON><PERSON><PERSON><PERSON>", "publicOfficer": "Pegawai <PERSON>", "publicOfficerList": "Senarai $t(publicOfficer)", "publicOfficerName": "Nama $t(publicOfficer)", "publicOfficerCreatedSuccessfully": "$t(publicOfficer) berjaya dicipta", "publicOfficerUpdatedSuccessfully": "$t(publicOfficer) ber<PERSON><PERSON> di<PERSON><PERSON>", "publicOfficialsInformation": "Maklumat Pegawai Awam", "purchasePurpose": "<PERSON><PERSON><PERSON>", "purposeAndPaymentSearch": "<PERSON><PERSON> da<PERSON>", "purposeInMalaysia": "<PERSON><PERSON><PERSON> berada di <PERSON>", "purposeOfTheMeeting": "<PERSON><PERSON>at <PERSON>", "reportingManagement": "<PERSON><PERSON><PERSON><PERSON> pelaporan", "race": "<PERSON><PERSON>", "rayuan": "<PERSON><PERSON>", "rayuanInformation": "Maklumat <PERSON>", "redFlag": "Red Flag", "referToOtherAgencies": "Rujuk <PERSON> (ROA)", "referToOtherComplaints": "<PERSON><PERSON><PERSON><PERSON> (ROR)", "referenceNumber": "No. Rujukan", "registerAJK": "Daftar AJK (warganegara)", "registerAuditor": "<PERSON><PERSON><PERSON>", "registerBankAccount": "Daftar akaun bank", "registerButton": "<PERSON><PERSON><PERSON>", "registerHere": "<PERSON><PERSON><PERSON>", "registerNonCitizenAJK": "Daftar AJK (bukan warganegara)", "registerOfOrganizationManagementGuidelines": "<PERSON><PERSON><PERSON>", "registerOrg": "<PERSON><PERSON><PERSON>", "registerOrganization": "<PERSON><PERSON><PERSON>", "registerPropertyOfficer": "Daftar Pegawai Hart<PERSON>", "registerPublicOfficer": "Daftar Pegawai Awam", "registerTrustee": "<PERSON><PERSON><PERSON>", "registeredAddressAndPlaceOfBusinessOfTheBranch": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON><PERSON> U<PERSON> Cawangan", "registeredAddressAndPlaceOfBusinessOfTheOrganization": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON><PERSON>", "registeredMemberAttendanceCount": "Bilangan kehadiran ahli berdaftar pada tarikh mesyuarat diadakan", "registeredMemberCount": "Bilangan ahli yang berdaftar", "religion": "<PERSON><PERSON>a", "remarks": "Catatan", "rent": "<PERSON><PERSON>", "rental": "<PERSON><PERSON>", "rentalIncome": "Pendapatan sewa", "reset": "<PERSON><PERSON><PERSON>", "resetForm": "<PERSON><PERSON><PERSON>", "resetPasswordButton": "<PERSON>", "residentialAddress": "<PERSON><PERSON><PERSON>", "residentialAddressCapitalizedOnlyFirstLetter": "<PERSON><PERSON><PERSON>", "residentialArea": "<PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON>", "reviewStatus": "Status semakan", "roRecommendation": "Syor RO", "rosieIntroduction": {"1": "Hai! <PERSON>a <PERSON> {{- emojiCode}}. <PERSON><PERSON><PERSON> saya bantu?", "2": "Saya halang skrin ke? K<PERSON> & alihkan saya ke tempat lain. {{- emojiCode}}", "closeMessage": "$t(status_Close) pesan"}, "sameAsAbove": "Tanda jika sama dengan atas", "sameAsBusinessAddress": "Sama $t(alamatTempatUrusan1)", "save": "Simpan", "saveChanges": "<PERSON><PERSON><PERSON>", "saveGoals": "Simpan matlamat", "sceneOfTheIncident": "Tempat Kejadian", "scholarship": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "searchDoc": "<PERSON><PERSON>", "searchInfo": "<PERSON><PERSON>", "secondGoals": "2. <matlamat 2>", "secretary": "<PERSON><PERSON><PERSON><PERSON>", "section": "Seksyen", "sectionDetails": "Butiran $t(section)", "selectAnnualStatementPresentationDate": "<PERSON><PERSON><PERSON> tarikh pem<PERSON><PERSON>n penyata tahunan", "selectFile": "<PERSON><PERSON><PERSON> fail", "selectIdType": "<PERSON><PERSON><PERSON>", "selectPaymentMethod": "<PERSON>la pilih cara pembayaran", "selectPlaceholder": "<PERSON><PERSON> pilih", "selfEmployed": "Bekerja <PERSON>", "semula": "<PERSON><PERSON><PERSON>", "senaraiAJKYangHadir": "Senarai AJK yang hadir", "senaraiAJKYangTidakHadir": "Senarai AJK yang tidak hadir", "senaraiDokumenPertubuhan": "<PERSON><PERSON><PERSON>", "senaraiRayuanPertubuhan": "<PERSON><PERSON><PERSON> rayuan <PERSON>", "sendingPEMNotification": "Penghantaran Notifikasi PEM", "services": "Perkhidmatan", "servicesIncome": "Perkhidmatan", "setNewPasswordButton": "Tetapkan kata laluan baru", "silaMasukkanNamaKodQR": "<PERSON>la masukkan nama kod QR", "silaPilih": "<PERSON><PERSON> pilih", "skillType": "<PERSON><PERSON>", "skills": "<PERSON><PERSON><PERSON>", "social": "Sosial", "startTime": "<PERSON>sa mula", "state": "<PERSON><PERSON><PERSON>", "stateOrganizationNote": "• <PERSON><PERSON> ta<PERSON><PERSON> pertub<PERSON>an <PERSON>, anda mestilah sama negeri dengan negeri pertubuhan.", "statementYearlyCashFlowDeclaration": "Dengan ini saya mengaku bahawa persatuan ini tidak mempunyai sebarang pendapatan & perbelanjaan pada tahun ini", "statusAwaitingApproval": "<PERSON><PERSON><PERSON>", "statusPermohonan": "Status Permohonan", "statusPertubuhan": "Status Per<PERSON>uhan", "statusRayuan": "Status Rayuan", "streetCatNGO": "NGO Kucing Jalanan", "supportingDocuments": "<PERSON><PERSON><PERSON>", "suspensionAndDismissalOfMembers": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan pemecatan ahli", "systemBackup": "Cadangan sistem: <PERSON><PERSON><PERSON>", "saveReport": "Simp<PERSON>", "titleLogin": "Selamat Datang ke eROSES Versi 2.0", "tangibleAssets": "<PERSON><PERSON> n<PERSON>", "tarafPertubuhan": "<PERSON><PERSON>", "tarikhMesyuarat": "<PERSON><PERSON><PERSON>", "tarikhPermohonan": "<PERSON><PERSON><PERSON>", "tarikhPlaceholder": "<PERSON><PERSON><PERSON> ta<PERSON>h", "tax": "C<PERSON><PERSON>", "tempatMesyuarat": "Tempat Mesyuarat", "tempohMasa": "Tempoh Masa", "tempohPengisianMaklumatCawanganDibuka": "Tempoh Pengisian Ma<PERSON>lumat Cawangan Dibuka", "templateMinitMesyuarat": "Template <PERSON><PERSON>", "terminationNotice": "Notis berhenti men<PERSON> ahli", "theEntityApplicationHasBeenRecorded": "Permohonan {{entity}} telah <PERSON>.", "thirdGoals": "3. ...", "thisComplaintHasBeenSuccessfullyReset": "<PERSON>uan Ini Telah Ber<PERSON>!", "thisComplaintHasBeenSuccessfullySubmittedAndRecorded": "<PERSON>uan Ini Telah Ber<PERSON>a Dihantar Dan Direkodkan!", "tiadaFailDipilih": "Tiada fail dipilih", "tidak": "Tidak", "timeOfIncident": "<PERSON><PERSON>", "times": "kali", "timeUnitHour_one": "{{count}} jam", "timeUnitHour_other": "{{count}} jam", "title": "Gelaran", "titleFeedback": "Tajuk", "titleRegister": "Daftar Pengguna Baru", "totalAssets": "<PERSON><PERSON><PERSON>", "totalCurrentAssets": "<PERSON><PERSON><PERSON>", "totalIncome": "<PERSON><PERSON><PERSON>", "totalIncomeAmount": "<PERSON><PERSON><PERSON>", "totalLiabilities": "<PERSON><PERSON><PERSON>", "transportation": "Pengangkutan", "treasurer": "<PERSON><PERSON><PERSON>", "trusteeName": "<PERSON><PERSON>", "trusteeInformation": "Maklumat Pemegang Amanah", "trusteeList": "<PERSON><PERSON><PERSON>", "tujuanMesyuarat": "<PERSON><PERSON><PERSON>", "twitter": "Twitter", "typeOfAction": "<PERSON><PERSON>", "typeOfFee": "<PERSON><PERSON>", "ucapanAluanPengerusi": "<PERSON><PERSON><PERSON>", "unpaidDeferredTaxes": "Cukai Tertunda Belum Dibayar", "unpaidTaxes": "<PERSON><PERSON><PERSON>", "update": "Kemaskini", "updateBankAccount": "Kemaskini Akaun Bank", "updateDocument": "Kemaskini", "updateForm": "Kemaskini", "updateOrgInfo": "Kemaskini maklum<PERSON> per<PERSON>an", "updateOrganizationInfo": "Kemaskini Maklumat <PERSON>", "uploadButton": "<PERSON><PERSON>", "uploadDocument": "Muat Naik Dokumen", "uploadId": "<PERSON>la muat naik gambar pengenalan diri anda dibawah", "uploadIdPlaceholder": "Muat naik gambar pengenalan diri anda disini", "uploadSelfie": "<PERSON>la muat naik gambar swafoto anda bersama pengenalan diri dibawah", "uploadSelfiePlaceholder": "Muat naik gambar swafoto anda bersama pengenalan diri disini", "userGuide": "Panduan Pengguna", "utilities": "Utiliti", "value": "<PERSON><PERSON> (RM)", "vehicles": "Kenderaan", "verificationNote": "* Pengesahan identiti anda akan mengambil masa maksima 3 hari. Anda akan boleh mula masuk selepas pengesahan identiti selesai. Anda akan menerima notifikasi melalui emel yang telah dimasukkan diatas. Sila pastikan emel tersebut adalah sah.", "verificationOfPropertyOfficers": "$t(pengesahan) $t(pegawaiHarta)", "viceChairman": "<PERSON><PERSON>", "vicePresident": "<PERSON><PERSON><PERSON>n", "view": "Lihat", "viewAJKInfo": "Lihat maklumat AJK", "viewAuditorInfo": "<PERSON><PERSON>", "viewDocument": "Lihat Dokumen", "viewMemberInfo": "<PERSON><PERSON>", "viewOrg": "<PERSON><PERSON>", "viewPropertyOfficerInfo": "Lihat Maklumat Pegawai Harta", "viewPublicOfficerInfo": "Lihat Maklumat Pegawai Awam", "viewTrusteeInfo": "<PERSON><PERSON> Pemegan<PERSON>", "visaExpiryDate": "<PERSON><PERSON><PERSON>", "visaNumber": "No. Visa", "weekly": "Mingguan", "webDesign": "<PERSON><PERSON>", "website": "<PERSON><PERSON>", "websiteMarkup": "<PERSON><PERSON><PERSON>", "week": "<PERSON><PERSON>", "welfareExpenses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "whitelistDate": "<PERSON><PERSON><PERSON>", "whitelisted": "Telah diputihkan", "whitelisting": "<PERSON><PERSON><PERSON><PERSON>", "whitelistingInformation": "Keterangan $t(whitelisting)", "whitelistingSuccessMessage": "{{- name}} telah dip<PERSON>!", "writeInWords": "<PERSON><PERSON> da<PERSON> per<PERSON>an", "ya": "Ya", "yearlyIncomeStatementHelper": "Sila isi maklumat penyata pendapatan untuk tahun kewangan ini.", "years": "tahun", "yes": "Ya", "yourComplaintHasBeenSuccessfullySubmitted": "<PERSON><PERSON>", "yourComplaintWillBeInvestigatedWithinDay_one": "<PERSON><PERSON> {{count}} Hari", "yourComplaintWillBeInvestigatedWithinDay_other": "<PERSON><PERSON> {{count}} Hari", "youthMember": "<PERSON><PERSON>", "youthMemberFirstOption": "Terbuka kepada mereka yang berumur 18 tahun ke bawah. <PERSON>reka perlu mendapatkan surat kebenaran bertulis daripada ibu bapa atau penjaga yang sah. <PERSON>reka tidak boleh mengundi atau memegang jawatan dalam <PERSON>.", "submitApplication": "<PERSON><PERSON> permohonan?", "confirmSubmitApplication": "<PERSON><PERSON>h anda pasti untuk menghantar permohonan ini?", "bilanganHari": "<PERSON><PERSON><PERSON><PERSON>", "constitutionInformation": "Maklumat Perlembagaan", "amendmentList": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "subCategory": "Sub-kategori", "download": "<PERSON>at turun", "amendmentInformation": "<PERSON>ndaan ma<PERSON>", "amendmentAll": "<PERSON><PERSON><PERSON>", "amendmentAllWithoutGuide": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>han tanpa panduan", "meetingName": "<PERSON><PERSON>", "meetingPlace": "Tempat Mesyuarat", "note": "Catatan", "meetingTime": "<PERSON><PERSON>", "attendance": "<PERSON><PERSON><PERSON>", "meetingMinutes": "<PERSON><PERSON>", "amendedClause": "<PERSON>asal yang dipinda", "reviewed": "Disemak", "notReviewed": "<PERSON>um disemak", "reviewConstitution": "Semakan <PERSON>", "viewMore": "<PERSON><PERSON>", "organizationKnownAs": "<PERSON><PERSON><PERSON>an ini dikenali dengan nama", "hereinafterReferredTo": "selepas ini disebut \"<PERSON><PERSON>uhan\".", "level": "<PERSON><PERSON><PERSON>", "amendmentType": "<PERSON><PERSON>", "applicationStatus": "Status Permohonan", "applicationDate": "<PERSON><PERSON><PERSON>", "submissionDate": "<PERSON><PERSON><PERSON>", "decisionDate": "<PERSON><PERSON><PERSON>", "viewAmendment": "<PERSON><PERSON>", "deleteAmendment": "<PERSON><PERSON> pin<PERSON>an", "constitutionFor": "Perlembagaan Bagi", "beforeAmendment": "Sebel<PERSON>", "afterAmendment": "<PERSON><PERSON><PERSON>", "sudahMempunyaiAkaun": "SUDAH MEMPUNYAI AKAUN? LOG MASUK", "PengesahanAkaun": "<PERSON><PERSON><PERSON>", "EnterCode": "Masukkan kod", "WeveSentAnActivationCodeToYourEmailAt": "<PERSON><PERSON> telah men<PERSON>tar kod pengaktifan ke emel anda di", "VerificationCodeIsRequired": "<PERSON><PERSON> pen<PERSON>an", "PengesahanIdentity": "Pengesahan identiti", "Langkah": "Lang<PERSON><PERSON>", "SendCodeAgain": "<PERSON><PERSON> kod semula", "PengesahanKataLaluan": "<PERSON><PERSON><PERSON>", "MaklumatPengenalan": "Maklumat Pengenalan Diri", "SilaLakukanPengesahanKadPengenalanDiri": "<PERSON>la lakukan pengesahan kad pengenalan diri", "SilaLakukanPengesahanDiriBersamaKadPengenalanDiri": "<PERSON>la lakukan pengesahan diri bersama kad pengenalan diri", "registeredEmail": "<PERSON><PERSON> yang berdaftar dengan akaun eRoses", "checkEmail": "<PERSON>la semak emel anda bagi men<PERSON>kan proses lupa kata laluan", "backToLogin": "Ke<PERSON>li ke log masuk", "notifikasi": "Notif<PERSON><PERSON>", "committeeListForYear": "<PERSON><PERSON><PERSON> ahli jawatank<PERSON>sa pada tahun", "passCertificate": "Sijil lulus", "senaraiMesyuaratHadir": "<PERSON><PERSON><PERSON><PERSON>", "jumlahKehadiranKodQr": "<PERSON><PERSON><PERSON> keh<PERSON>ran mengikuti KOD QR", "jumlahKehadiranSeluruhAhliMesyuarat": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON> keh<PERSON> ahli", "petaLokasi": "<PERSON><PERSON>", "addAjk": "Tambah AJK", "jenisPertubuhan": "<PERSON><PERSON>", "pengurusanAjk": "AJK & Keahlian", "penubuhanInduk": "<PERSON><PERSON><PERSON><PERSON>", "penubuhanCawangan": "<PERSON><PERSON><PERSON><PERSON>", "kuiriPertubuhan": "<PERSON><PERSON>", "penubuhanCawangan2": "<PERSON><PERSON><PERSON><PERSON>", "kuiriCawangan": "<PERSON><PERSON>", "lanjutanMasa": "<PERSON><PERSON><PERSON><PERSON>", "tarikhBayar": "<PERSON><PERSON><PERSON>", "tarikhMohon": "<PERSON><PERSON><PERSON>", "namePemohon": "<PERSON><PERSON>", "kelulusanPermohonanLanjutanMasa": "<PERSON><PERSON><PERSON><PERSON> permohonan lanjutan masa", "bilanganHariDipohon": "<PERSON><PERSON><PERSON> <PERSON> ya<PERSON>", "justifikasiPermohonan": "<PERSON><PERSON><PERSON><PERSON>", "noPPMCawangan": "No PPM Cawangan", "noPPMInduk": "No PPM Induk", "emelPemohon": "<PERSON><PERSON>", "roBertanggungjawab": "<PERSON><PERSON>", "tarikhAlir": "<PERSON><PERSON><PERSON>", "catatan": "Catatan (had 100 perkataan)", "lulus": "<PERSON><PERSON>", "keputusanPermohonan": "Keputusan Permohonan", "permohonanConfirmation": "<PERSON><PERSON>h anda pasti dengan keputusan permohonan ini?", "bukanWarganegaraInduk": "Bukan Warganegara Induk", "bukanWarganegaraCawangan": "Bukan Warganegara Cawangan", "applicationType": "<PERSON><PERSON>", "ppmNumber": "No PPM", "responsibleRO": "<PERSON><PERSON>", "roSection": "Seksyen RO", "nonCitizenAJKDecision": "Keputusan AJK Bukan Warganegara", "applicationDecision": "Keputusan Permohonan", "decision": "Keputusan", "nonCitizenAJKInfo": "Maklumat AJK Bukan Warganegara", "ajkQualificationList": "<PERSON><PERSON><PERSON>", "importanceOfNonCitizen": "Kepentingan Buka<PERSON>", "limitedTo100Words": "had 100 perkataan", "applicantList": "<PERSON><PERSON><PERSON>", "action": "<PERSON><PERSON><PERSON>", "residentialPurpose": "Be<PERSON><PERSON><PERSON><PERSON>", "awaitingDecision": "Menunggu keputusan", "utama": "<PERSON><PERSON><PERSON>", "kelulusanPertubuhan": "<PERSON><PERSON><PERSON><PERSON>", "penyelenggaraanPertubuhan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "carianPertubuhan": "<PERSON><PERSON>", "kemaskiniPertubuhan": "<PERSON><PERSON><PERSON>", "penyelenggaraan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pengurusanPengguna": "<PERSON><PERSON><PERSON><PERSON>", "pengurusanPeranan": "<PERSON><PERSON><PERSON><PERSON>", "penyelenggaraanUmum": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "auditTrail": "Audit Trail", "piagamPerlembagaan": "Piagam Perlembagaan", "mejaBantuan": "<PERSON><PERSON>", "senaraiAduan": "<PERSON><PERSON><PERSON>/<PERSON><PERSON>/<PERSON><PERSON><PERSON>", "perkhidmatanKaunter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "senaraiWarta": "<PERSON><PERSON><PERSON>", "muatTurunWarta": "<PERSON><PERSON>", "papar": "<PERSON><PERSON>", "branchedStatus": "Status Bercawangan", "viewAjk": "Semak AJK", "orgNumber": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "identificationNumber": "No. Pengenalan Diri", "reviewSummary": "Rumusan Semakan", "orgAndApprovalInfo": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "externalAgencyReviews": "<PERSON>las<PERSON> agensi luar", "answerDate": "<PERSON><PERSON><PERSON>", "rejectionReason": "<PERSON><PERSON><PERSON>", "menungguAgensiLuar": "<PERSON>ung<PERSON> Agensi <PERSON>", "queryHistory": "<PERSON><PERSON><PERSON>", "acceptExternalAgencyReviews": "<PERSON><PERSON><PERSON>", "reviewDate": "<PERSON><PERSON><PERSON>", "catatanRO": "Catatan (Terhad kepada 100 patah perkataan)", "sectionRO": "Sekyen RO", "maklumatPermohonan": "Maklum<PERSON>", "termsAndPrivacyAgreement": "<PERSON>gan meneruskan pendaftaran ini, anda bersetuju dengan", "termsOfUse": "<PERSON><PERSON>", "privacyPolicy": "<PERSON><PERSON>", "mainPage": "<PERSON><PERSON>", "VerificationCodeMustBe6Digits": "<PERSON><PERSON> pengesahan mestilah 6 digit", "EmailVerification": "Pengesahan E-mel", "ActivationCodeSentTo": "Kod pengaktifan telah dihantar ke e-mel", "Verify": "Mengesahkan", "YourEmailHasBeenVerified": "<PERSON><PERSON> anda telah di<PERSON>.", "Continue": "Teruskan", "clickToLogin": "Klik Untuk Log Masuk", "ResendCodeIn": "<PERSON><PERSON>", "PhoneVerification": "Pengesahan Nombor Telefon", "VerificationCodeSentTo": "Kod pengaktifan telah dihantar ke nombor", "YourPhoneHasBeenVerified": "Nombor Telefon anda telah di<PERSON>.", "PasswordIsRequired": "<PERSON><PERSON> dip<PERSON>an", "PasswordMinLength": "Kata laluan mestilah sekurang-kurangnya 8 aksara", "ConfirmPasswordIsRequired": "<PERSON><PERSON><PERSON>an", "PasswordsMustMatch": "<PERSON>a laluan mestilah sama", "YourPasswordHasBeenVerified": "<PERSON>a la<PERSON>an anda telah disa<PERSON>.", "UseAtLeast8Characters": "<PERSON><PERSON><PERSON><PERSON> sekurang-<PERSON><PERSON><PERSON><PERSON> 8 aksara, dengan huruf besar, huruf k<PERSON>il, dan satu aksara khas", "PasswordConfirmation": "<PERSON><PERSON><PERSON>", "PasswordsDoNotMatch": "Kata laluan yang dimasukkan tidak sama. Sila cuba sekali lagi.", "identityVerification": "Pengesahan Identiti", "informationNeeded": "<PERSON><PERSON><PERSON> adalah maklumat yang kami perlukan daripada anda.", "selfVerification": "<PERSON><PERSON><PERSON>", "takePhotoInstructions": "<PERSON>la dapatkan gambar wajah mengikut arahan yang diberikan.", "idCardInformation": "Maklumat Kad Pengenalan", "verifyValidIdCard": "Sila lakukan pengesahan kad pengenalan yang sah.", "agreeToProvideData": "<PERSON>gan mengklik <PERSON>rima dan <PERSON>, anda bersetuju untuk memberikan data yang diminta.", "acceptAndContinue": "<PERSON><PERSON> dan lan<PERSON>kan", "invalidEmail": "<PERSON><PERSON>t e-mel tidak sah", "setNewPassword": "Tetapkan kata laluan baru", "createNewPasswordInstructions": "Cipta kata laluan baru. Pastikan ia berbeza daripada yang sebelumnya demi keselamatan.", "fieldRequired": "Medan ini diperlukan", "passwordMinLength": "Kata laluan mestilah sekurang-kurangnya 8 aksara", "passwordsMustMatch": "<PERSON>a laluan mestilah sama", "passwordsDoNotMatch": "Kata laluan yang dimasukkan tidak sama. Sila cuba sekali lagi.", "setPassword": "Tetapkan Kata <PERSON>", "passwordSet": "<PERSON><PERSON>", "passwordChangedSuccessfully": "<PERSON>a la<PERSON> anda telah berjaya di<PERSON>. Klik teruskan untuk log masuk.", "identityVerificationConfirmed": "Pengesahan identiti anda telah disahkan.", "scanQRCodeInstructions": "Imbas kod QR di bawah menggunakan telefon bimbit anda", "howEKYCWorks": "Bagaimana eKYC berfungsi", "scanQRCode": "<PERSON><PERSON><PERSON>", "people": "Orang", "ordinaryCommitteeMember": "<PERSON><PERSON>", "padamPermohonan": "<PERSON><PERSON>", "pilihMesyuaratPembentangan": "<PERSON><PERSON><PERSON> pembentangan pindaan per<PERSON>aan", "keputusanPermohonanPindaan": "Keputusan <PERSON>", "keputusan": "Keputusan", "pindaanUndang": "<PERSON><PERSON><PERSON>", "maintenancerGeneral": "Penyelenggara Umum", "penggunaJPM": "Pengguna JPM", "categoryPenggunaJPM": "Kategori Pen<PERSON>una JPM", "maklumatPindaan": "Maklumat Pindaan", "namaRingkasPertubuhan": "<PERSON><PERSON>", "pilihanPerlembagaan": "<PERSON><PERSON><PERSON>", "sertaPertubuhan": "<PERSON><PERSON>", "pembaharuanSetiausaha": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pembaharuanSetiausahaCawangan": "<PERSON><PERSON>bah<PERSON><PERSON>", "activityLists": "Senarai aktiviti", "saveSearch": "<PERSON><PERSON><PERSON> carian", "searchInformation": "<PERSON><PERSON>", "enterMeetingName": "<PERSON><PERSON><PERSON><PERSON>", "registerMeeting": "<PERSON><PERSON><PERSON>", "meetingList": "<PERSON><PERSON><PERSON>", "meetingType": "<PERSON><PERSON>", "status": "Status", "submodule": "Submodul", "delete": "Padam", "meetingInformation": "<PERSON><PERSON>", "pleaseSelect": "<PERSON><PERSON> pilih", "meetingMethod": "<PERSON><PERSON><PERSON>", "platformType": "Jenis Platform", "meetingPurpose": "<PERSON><PERSON><PERSON>", "time": "<PERSON><PERSON>", "locationMap": "<PERSON><PERSON>", "meetingPlaceAddress": "<PERSON><PERSON>t Tempat Mesyuarat", "meetingCall": "Panggilan <PERSON>", "fillMeetingInformation": "<PERSON><PERSON>", "meetingAttendanceList": "<PERSON><PERSON><PERSON>", "attendanceByQR": "<PERSON><PERSON><PERSON>n <PERSON> QR", "updateAttendance": "<PERSON><PERSON><PERSON>", "present": "<PERSON><PERSON>", "absent": "Tidak Hadir", "taskFlowInfo": "<PERSON><PERSON><PERSON><PERSON> dibenarkan untuk mengalirkan tugas jika perlu. <PERSON><PERSON><PERSON><PERSON> dibenarkan untuk memilih mana mana AJK untuk mengendalii tugas pengisian maklumat mesyuarat. Set<PERSON>usa<PERSON> dibenarkan memilih lebih dari seorang wakil AJK untuk membantu pengisian maklumat mesyuarat.", "taskFlow": "<PERSON><PERSON>", "selectTaskFlowTo": "<PERSON><PERSON><PERSON> al<PERSON>n tugas kepada", "taskFlowList": "<PERSON><PERSON><PERSON>", "taskFlowStatus": "Status <PERSON><PERSON>", "taskFlowDate": "<PERSON><PERSON><PERSON>", "deactivationDate": "Tarikh Nyah Aktif", "active": "Aktif", "inactive": "Tidak Aktif", "confirmTaskFlowChange": "<PERSON><PERSON><PERSON> anda pasti untuk mengalir tugas Pengurusan Mesyuarat kepada Jawatankuasa lain?", "deactivateTaskFlow": "Nyah aktif alir tug<PERSON>", "confirmDeactivateTaskFlow": "<PERSON><PERSON><PERSON> anda pasti untuk nyah aktif alir tugas Penyata Tahun<PERSON> da<PERSON>ada <PERSON>tankuasa ini?", "organizationTitle": "{{name}} / {{number}}", "deleted": "DIPADAM", "maklumatMajikanWarning": "*<PERSON>gi ahli jawata<PERSON> yang TIDAK BEKERJA/ PESARA, maklumat majikan tidak perlu diisi", "marketField": "<PERSON><PERSON> bertanda", "mandatoryHere": "adalah mandatory disini.", "ajkBukanWnWarning": "Pegang Jawatan bagi Bukan Warganegara ini adalah untuk mendaftar ahli jawatankuasa lain yang bukan warganegara Malaysia.", "maklumat": "Maklumat", "kemaskiniCawangan": "Kemaskini Cawangan", "pengurusCawangan": "<PERSON><PERSON><PERSON>", "kelulusan": "<PERSON><PERSON><PERSON><PERSON>", "organizationEstablishmentGeneralMeeting": "Mesyua<PERSON>", "mawarCommunityHallGombak": "<PERSON><PERSON>, Gombak", "notSubmitted": "<PERSON><PERSON>", "completed": "Se<PERSON><PERSON>", "committeeMeeting": "Mesyuarat AJK", "meetingNoticeSent": "NOTIS MESYUARAT DIHANTAR", "awaitingCommitteeResponse": "MENUNGGU MAKLUMBALAS JAWATANKUASA", "enterOrgNameOrNumber": "<PERSON><PERSON><PERSON><PERSON> atau No. Pertubuhan", "searchOrganization": "<PERSON><PERSON>", "noOrganizationFound": "<PERSON><PERSON><PERSON><PERSON> tiada dalam rekod", "join": "Sertai", "cancel": "<PERSON><PERSON>", "alreadyMember": "<PERSON><PERSON> men<PERSON> ahli", "maklumatPengguna": "Maklumat Pengguna", "categoryPengguna": "Category Pengguna", "carianNama": "<PERSON><PERSON>", "senaraiPengguna": "Sen<PERSON><PERSON>", "bilPengguna": "<PERSON><PERSON>", "tambahPengguna": "Tambah Pengguna", "maklumatPenggunaJPPM": "Maklumat Pengguna JPPM", "gelaran": "Gelaran", "peranan": "<PERSON><PERSON><PERSON>", "statusLogMasuk": "Status Log Masuk", "idUser": "ID Pengguna", "createBy": "<PERSON><PERSON><PERSON>", "lastUpdate": "<PERSON><PERSON><PERSON>", "lastUpdateChangePassword": "Ke<PERSON><PERSON> Te<PERSON><PERSON>", "categoryPenggunaJPPM": "Kategori <PERSON>una JPPM", "addCategoryPenggunaJPPM": "Tambah Kategori <PERSON>anan JPPM", "descriptionCategory": "Keteranga<PERSON>", "ketetapanKebenaranAkses": "<PERSON><PERSON><PERSON>", "add": "Tambah", "keputusanJawatankuasa": "Keputusan Jawatankuasa", "maklumatPermohonanPembaharuanSetiausaha": "Maklumat <PERSON>", "maklumbalasJawatankuasa": "Mak<PERSON>balas J<PERSON>", "warningPertubuhan": "! Pertubuhan tiada dalam rekod. Sila hubungi JPPM Negeri untuk menyemak status pertubuhan anda", "confirmActiveTugas": "<PERSON><PERSON><PERSON> anda pasti untuk pengaktifan alir tugas <PERSON> daripada Jawatankuasa ini?", "pengaktifanAlirFlow": "Pengakt<PERSON><PERSON>", "welcomeTo": "Selamat Datang", "welcome": "Selamat Datang", "passwordRequired": "<PERSON><PERSON> dip<PERSON>an", "nationalEmblem": "<PERSON><PERSON>", "logo": "Logo", "phoneNumberPlaceholder": "1 23 45 67 89", "and": "dan", "notReceivedEmail": "Belum terima emel?", "passwordSetSuccessfully": "<PERSON>a laluan telah berjaya diteta<PERSON><PERSON>", "continue": "Teruskan", "Password": "<PERSON><PERSON>", "Step": "Lang<PERSON><PERSON>", "Login": "Log Ma<PERSON>k", "identificationCard": "Kad <PERSON>", "identification": "Pengenalan diri", "passportNumber": "No. Passport", "idNumberRequired": "Masukkan No. Pengenalan Diri yang berdaftar", "toEroses": "ke eROSES", "resetPasswordInstructions": "<PERSON><PERSON> telah men<PERSON>tar pautan tetapan semula ke {{email}}. <PERSON>la masukkan kod 6 digit yang dinyatakan dalam e-mel.", "day": "<PERSON>", "month": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>", "biennial": "Dwitahunan", "tahun": "tahun sekali", "setahun": "<PERSON><PERSON><PERSON>", "duaTahun": "<PERSON><PERSON> (2) tahun sekali", "presiden": "Presiden", "presidenCawangan": "Presiden <PERSON>awa<PERSON><PERSON>", "pengarah": "Pengara<PERSON>", "pengarahCawangan": "<PERSON><PERSON><PERSON>", "timbalanPengerusi": "<PERSON><PERSON><PERSON>", "timbalanPengarah": "<PERSON><PERSON><PERSON>", "naibPengerusi": "<PERSON><PERSON>", "timbalanPengerusiCawangan": "<PERSON><PERSON><PERSON>", "naibPresiden": "<PERSON><PERSON>", "timbalanPresidenCawangan": "<PERSON><PERSON><PERSON> Presiden <PERSON><PERSON><PERSON>", "naibPengarah": "<PERSON><PERSON>", "naibPengarahCawangan": "<PERSON><PERSON>", "generalSecretary": "<PERSON><PERSON><PERSON><PERSON>", "generalAssistantSecretary": "Penolong <PERSON>", "generalTreasurer": "<PERSON><PERSON><PERSON>", "chiefTreasurer": "<PERSON><PERSON><PERSON>", "honoraryTreasurer": "<PERSON><PERSON><PERSON>", "generalAssistantTreasurer": "Penolong Bendahari Agung", "chiefAssistantTreasurer": "Penolong <PERSON>", "honoraryAssistantTreasurer": "Penolong <PERSON>", "monthly": "Bulanan", "tempohDibenarkan": "Tempoh yang dibenarkan untuk memasukkan wang lebihan ke dalam bank (Hari)", "tahunKewanganBermula": "<PERSON><PERSON>", "jumlahWangTangan": "<PERSON><PERSON><PERSON> wang yang diben<PERSON>an dalam tangan", "kuasaPerbelanjaan": "<PERSON><PERSON> (RM)", "kuasaPerbelanjaanCaw": "<PERSON><PERSON>", "kuasaPerbelanjaanCawangan": "<PERSON><PERSON> per<PERSON>an me<PERSON> agung cawangan", "kuasaPerbelanjaanCawangan2": "<PERSON><PERSON>", "kuasaPerbelanjaanJawatankuasa": "<PERSON><PERSON> per<PERSON> jaw<PERSON>", "perbelanjaanDibenarkan": "<PERSON><PERSON><PERSON><PERSON><PERSON> yang diben<PERSON>an untuk dilulus<PERSON> o<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "perbelanjaanDibenarkanCawangan": "<PERSON><PERSON><PERSON><PERSON><PERSON> yang dibenarkan untuk diluluskan oleh <PERSON>, <PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "internal": "<PERSON><PERSON>", "external": "<PERSON><PERSON><PERSON>", "internalAuditorNumber": "<PERSON><PERSON><PERSON><PERSON>", "externalAuditorNumber": "Bilanga<PERSON>", "jenisMesyuaratAgung": "<PERSON><PERSON>", "tempohPelaksanaan": "Tempoh pelaks<PERSON>an mesyuarat agung baru daripada tarikh te<PERSON>hir <PERSON>gung", "kekerapanPelaksanaan": "Kekerapan pelaksanaan mesyuarat agung baru", "notisPanggilanMesyuarat": "<PERSON>is <PERSON>", "keteranganBendera": "Keterangan Bendera", "bendera": "<PERSON><PERSON>", "keteranganLambang": "Keterangan Lambang", "lambang": "Lambang", "keteranganLencana": "Keterangan Lencana", "lencana": "Lencana", "alamatUrusan": "<PERSON><PERSON><PERSON>", "alamatSuratMenyurat": "<PERSON><PERSON><PERSON>", "bubarPertubuhan": "<PERSON><PERSON><PERSON>", "bubarPertubuhanConfirm": "<PERSON>dul ini bertujuan untuk membatalkan pertubuhan di bawah Seksyen 13(1)(a). <PERSON><PERSON><PERSON> anda pasti untuk membubarkan pertubuhan?", "bankruptcy": "bankrupsi", "saving": "Menyimpan", "requiredValidation": "<PERSON>la isi ruangan mandatori", "paparMesyuarat": "<PERSON><PERSON>", "checkAJK": "Check <PERSON>", "comingSoon": "<PERSON><PERSON>", "comingSoonMessage": "Nantikan ciri-ciri yang menarik tidak lama lagi", "internalUser": "<PERSON><PERSON><PERSON>", "organizationUser": "Pengg<PERSON> Pertubuhan", "meetAndFinanceInformation": "Maklumat me<PERSON>uarat dan kewangan", "maklumatAsset": "Maklumat Aset", "maklumatAssetCampaign": " <PERSON> penye<PERSON>aian pada baki aset hendaklah selaras dengan keputusanmesyuarat agung pembubaran.", "positionInfo": "Maklumat Jawatan", "reference_number": "Nombor rujukan", "organization_category": "<PERSON><PERSON><PERSON>", "sub_category": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "organization_name": "<PERSON><PERSON>", "organization_phone": "No. telefon <PERSON>", "financial_year_start": "<PERSON>hun kewangan bermula", "registered_members_count": "Bilangan ahli yang berdaftar", "office_bearers_count": "Bilangan Pemegang Jawatan", "office_bearers_count_disabled": "Bilangan Pemegang Jawatan", "branches_count": "Bilanga<PERSON> (Jika <PERSON>a)", "affiliations": "<PERSON><PERSON><PERSON><PERSON>/ <PERSON><PERSON>kutu dengan <PERSON> dalam negeri dan atau luar negeri", "tolak": "<PERSON><PERSON>", "pending": "<PERSON><PERSON>", "infoPernyataTahunan": "Penyata <PERSON>an hendaklah dikemaskini dalam masa 60 hari selepas tarikh Me<PERSON>uarat <PERSON> . <PERSON>gi pertubuhan yang tidak mengadakan mesyuarat pada tahun tersebut, penyata hendaklah dikemaskini sebelum 1 Mac.", "maklumatPemohon": "Maklumat Pemohon", "kuiri": "Ku<PERSON>", "catatanKuiri": "Catatan Kuiri", "personalInfoNewSec": "Maklumat <PERSON>", "inputPlaceholder": "<PERSON>la isi", "mobileAddition": "(Bimbit)", "houseAddition": "(<PERSON><PERSON><PERSON>)", "officeAddition": "(<PERSON><PERSON><PERSON><PERSON>)", "pelantikanSetiausaha": "Maklumat Mesyuarat Pelantikan Setiausaha Baru", "sebabTukarSetiausaha": "<PERSON><PERSON><PERSON>", "personalInfoOldSecretary": "<PERSON><PERSON><PERSON><PERSON>", "confirmOldSecretary": "<PERSON>a mengaku bahawa saya adalah orang yang telah dilantik sebagai Setiausaha bagi mengemaskini maklumat pertubuhan.", "ulasan": "<PERSON><PERSON><PERSON>", "pengakuanPembaharuan": "<PERSON><PERSON><PERSON> permohonan pembah<PERSON>an setia<PERSON>ha pertubuhan ini adalah <PERSON>.", "pengakuanTitle": "<PERSON><PERSON><PERSON><PERSON> pem<PERSON><PERSON><PERSON> set<PERSON>", "enterSearch": "<PERSON><PERSON><PERSON><PERSON> carian", "maklumatSetiausahaBaru": "<PERSON><PERSON><PERSON><PERSON>", "maklumatSetiausahaLama": "<PERSON><PERSON><PERSON><PERSON>", "maklumBalas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statusPemegangJawatan": "Status pemegang jawatan", "pengakuan": "<PERSON><PERSON><PERSON><PERSON>", "senaraiMaklumbalasJawatankuasa": "<PERSON><PERSON><PERSON>", "cetak": "Cetak", "statusCawangan": "Status Cawangan", "selectSociety": "<PERSON><PERSON><PERSON>", "selectBranch": "<PERSON><PERSON><PERSON>", "cawanganNumber": "No. Cawangan", "pelantikanMesyuarat": "Maklumat mesyuarat pelantikan setiausaha baru cawangan", "cawangan": "Cawangan", "checkSetiausahaBaruCawangan": "<PERSON>a mengaku bahawa penama di atas adalah orang yang telah dilantik sebagai Setiausaha C<PERSON>ngan bagi mengemaskini maklumat cawangan ini.", "pengesahan": "<PERSON><PERSON><PERSON>", "infoPayment": "<PERSON><PERSON><PERSON><PERSON> pendaftaran pertubuhan telah dire<PERSON>.", "infoPaymentCawangan": "<PERSON><PERSON><PERSON><PERSON> pendaftaran cawangan telah dire<PERSON>.", "infoPaymentPindaanCawangan": "<PERSON><PERSON><PERSON><PERSON> pindaan cawangan telah <PERSON>.", "paymentAmount": "<PERSON><PERSON><PERSON> yang perlu di<PERSON>ar", "noteKaunter": "Nota: Sila cetak slip ini untuk dibawa semasa pembayaran di kaunter.", "noteOnline": "Nota: <PERSON><PERSON>/ <PERSON>uan boleh cetak slip ini untuk rujukan sendiri.", "paymentType": "<PERSON><PERSON>", "amanah": "Kod OSOL/ Amanah", "jabatan": "Jabatan", "pusatPenerimaan": "PTJ/ Pusat terimaan", "bayar": "Bayar", "terms": "Terma & Syarat", "term1": "Semakan maklumat pembayaran perlu dibuat sebelum pembayaran dapat dilaksanakan. Sila pastikan maklumat adalah tepat.", "term2": "<PERSON>la pastikan anda menutup/Disable Popup Blocker pada pelayan web (web browser) anda sebelum membuat pembayaran. <PERSON>la ikut arahan pada skrin transaksi sehingga proses pembayaran selesai dan resit bayaran dipaparkan. Pihak kami tidak bertanggungjawab sekiranya skrin resit dan skrin pengesahan transaksi tidak dapat dipaparkan akibat masalah popup blocker di browser anda.", "term3": "<PERSON>la jangan tutup 'pop-up page' yang dipaparkan semasa transaksi FPX dijalankan supaya transaksi dapat dilaksanakan dengan berjaya.", "term4": "Caj perkhidmatan secara FPX yang dikenakan bagi setiap transaksi akan ditanggung oleh Jabatan Pendaftaran Pertubuhan Malaysia", "term5": "Sila cetak atau simpan (save) resit transaksi bayaran. Resit ini adalah sah dan boleh digunakan sebagai bukti pembayaran.", "term6": "Tempoh kemaskini pembayaran adalah selama 2 hari bekerja (Jika tiada masalah berkaitan teknikal sistem).", "term7": "Waktu operasi perkhidmatan pembayaran melalui FPX adalah dari jam 7 pagi hingga 11 malam setiap hari termasuk hari cuti dan kelepasan am.", "term8": "<PERSON>la pembayaran yang telah dibuat tidak boleh dikembalikan.", "term9": "<PERSON><PERSON>ng masalah sila hubungi Jabatan Pendaftaran Pertubuhan Malaysia di talian 03-8890 5776 / 03-8890 5778 atau <NAME_EMAIL>", "term10": "Kad K<PERSON>it tidak dibenarkan dalam sistem ini.", "setuju": "<PERSON><PERSON>", "butiran": "<PERSON><PERSON><PERSON>", "infoButiran": "<PERSON>la isi butiran anda di bawah untuk meneruskan pembayaran.", "perbankan": "Perbankan dalam italian", "modPembayaran": "<PERSON><PERSON>", "pilihanBank": "Pilihan Bank", "noteButiran": "<PERSON>gan mengklik butang '<PERSON><PERSON><PERSON>', anda bersetuju dengan Terma & Syarat FPX.", "maklumatPertubuhanDanCawangan": "Mak<PERSON><PERSON> dan <PERSON>", "kebangsaan": "<PERSON><PERSON><PERSON><PERSON>", "pembayaran": "Pembayaran", "warta": "Warta", "migrasiPerlembagaan": "<PERSON><PERSON><PERSON>", "tempohPelantikanWakilCawangan": "Tempoh Pelantikan Wakil Cawangan", "tempohPelantikanWakilAhli": "Tempoh Pelantikan Wakil <PERSON>", "bilanganWakilCawangan": "Bilangan Wakil Cawangan Terpilih", "bilanganWakilCawanganPertama": "Bilangan Wakil Cawanga<PERSON>", "bilanganWakilCawanganAhli": "Bilangan Wakil Cawangan Ah<PERSON>", "bilanganWakilCawanganAhliSeterusnya": "Bilangan Wakil Cawangan Ahli Seterusnya", "bilanganWakilAhli": "Bilangan W<PERSON>", "bilanganWakilAhliPertama": "Bilangan <PERSON>", "bilanganWakilAhliSeterusnya": "Bilangan W<PERSON> Seterusnya", "maksimumWakilCawangan": "<PERSON><PERSON><PERSON><PERSON>", "maksimumWakilAhli": "<PERSON><PERSON><PERSON><PERSON>", "invalidPhoneNumber": "<PERSON>la masukkan nombor telefon Malaysia yang sah bermula dengan +60", "phoneNumberHelper": "Format: +60", "emailHelper": "Format: <EMAIL>", "carianAlert": "Sila pastikan maklumat yang dimasukkan dengan tepat", "paymentDate": "<PERSON><PERSON><PERSON>", "bilanganCarianDokumen": "Bilangan Carian Doku<PERSON>: {{count}}", "senaraiMaklumatCarian": "<PERSON><PERSON><PERSON>", "messageMaklumatAmSuccess": "Maklumat Am berjaya disimpan", "messageMaklumatAmError": "Maklumat Am gagal disimpan", "userProfile": "<PERSON><PERSON>", "contactInfo": "Maklumat Perhubungan", "changePassword": "<PERSON><PERSON>", "oldPassword": "<PERSON><PERSON>", "rulePasswordOne": "Kata laluan hendaklah dalam lingkungan 8 hingga 15 aksara", "rulePasswordTwo": "Katalaluan mestilah dalam kombinasi huruf besar dan kecil dan nombor beseta simbol (!,@%,#)", "aduanMakluman": "Aduan/Ma<PERSON>lum balas", "pembayaranKaunter": "<PERSON><PERSON><PERSON><PERSON>", "rekodPembayaran": "<PERSON><PERSON><PERSON>", "semakanKaunterIndividu": "<PERSON><PERSON><PERSON>", "noRujukanPembayaran": "No. R<PERSON><PERSON><PERSON>", "noKpPemohon": "No. K/P <PERSON>", "bayaranDiterima": "Bayaran Diterima", "keteranganPembayaran": "Keterangan Pembayaran", "amaunPembayaran": "<PERSON><PERSON><PERSON>", "kodHasil": "<PERSON><PERSON>", "noResit": "<PERSON><PERSON><PERSON> (E-terimaan)", "namaPembayar": "<PERSON><PERSON>", "noPengenalanPembayar": "No. Pengenalan Pembayar", "negeriBayaranDiterima": "<PERSON><PERSON><PERSON>", "pegawaiBertugas": "Pegawai Bert<PERSON>s", "minimumJumlahAhliPertubuhan": "Minimum <PERSON><PERSON><PERSON>", "messageMaklumatMesyuaratPenubuhanSuccess": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ber<PERSON>a disimpan", "messageMaklumatMesyuaratPenubuhanError": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> gagal disimpan", "emailRegisteredPleaseLogin": "<PERSON>ail anda telah did<PERSON>, sila log masuk", "phoneNumRegisteredPleaseLogin": "Nombor telefon anda telah did<PERSON>, sila log masuk", "emailOrPhoneRegisteredPleaseLogin": "Email atau nombor telefon anda telah did<PERSON>, sila log masuk", "kodOsolAmanah": "Kod OSOL/Amanah", "Successfully uploaded Document": "<PERSON><PERSON><PERSON> ber<PERSON>a disimpan", "File sudah masuk database": "Sukses upload file database", "BELUM_DIHANTAR": "<PERSON><PERSON>", "MENUNGGU_KEPUTUSAN": "Menunggu <PERSON>", "LULUS": "<PERSON><PERSON>", "TOLAK": "<PERSON><PERSON>", "MENUNGGU_BAYARAN_KAUNTER": "<PERSON><PERSON><PERSON>", "MENUNGGU_BAYARAN_ONLINE": "Menunggu Bayaran Online", "MAKLUMAT_TIDAK_LENGKAP": "Maklumat Tidak Lengkap", "LUPUT": "Luput", "DIBENARKAN": "<PERSON><PERSON><PERSON><PERSON>", "TIDAK_DIBENARKAN": "Tidak Dibenarkan", "AKTIF": "Aktif", "MENUNGGU_PENGAKTIFAN_CAWANGAN": "Menunggu Pengaktifan <PERSON>", "TIADA_MAKLUMAT_MIGRASI": "Tiada Ma<PERSON>", "TAMAT_TEMPOH_CARIAN": "Tamat Tempoh Carian", "SETIAUSAHA_BUKAN_WARGA": "<PERSON><PERSON><PERSON><PERSON>", "BUBAR": "<PERSON><PERSON><PERSON>", "SELESAI": "Se<PERSON><PERSON>", "DISYORKAN": "Disyorkan", "TIDAK_DISYORKAN": "Tidak Disyorkan", "BATAL": "<PERSON><PERSON>", "MENUNGGU_SYOR_PEGAWAI_PEMPROSES": "Menunggu Syor <PERSON> Pemproses", "TEST_DATA": "<PERSON>", "MENUNGGU_MAKLUMBALAS": "<PERSON><PERSON><PERSON>", "PERTELINGKAHAN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TARIK_BALIK": "<PERSON><PERSON>", "BATAL_KHAS": "<PERSON><PERSON>", "MENUNGGU_PENGESAHAN_JAWATANKUASA_INDUK": "<PERSON><PERSON><PERSON> Pengesahan Jawatankuasa Induk", "LULUS_BERSYARAT": "<PERSON><PERSON>", "DALAM_TINDAKAN_KIV": "<PERSON><PERSON> KIV", "KUIRI": "Ku<PERSON>", "PINDAH": "Pindah", "MENUNGGU_PENGESAHAN_BAYARAN": "<PERSON><PERSON>gu <PERSON>aran", "MENUNGGU_KEPUTUSAN_MENTERI": "Menunggu Keputusan Menteri", "MENUNGGU_ULASAN": "<PERSON><PERSON><PERSON>", "MENUNGGU_ULASAN_AGENSI_LUAR": "<PERSON><PERSON><PERSON>", "NOTIS_MESYUARAT_DIHANTAR": "<PERSON><PERSON>", "INAKTIF": "<PERSON><PERSON><PERSON><PERSON>", "BAYARAN_GAGAL": "Bayaran Gagal", "BAYARAN GAGAL": "Bayaran Gagal", "confirmDeletePerlembagaan": "<PERSON><PERSON>h anda pasti untuk padam perlembagaan ini?", "confirmSend": "<PERSON><PERSON>h anda pasti untuk hantar AJK?", "deleteOrganizationConfirmation": "<PERSON><PERSON>h anda pasti untuk menghapus pertubuhan ini?", "deleteSuccess": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a dipadam", "deleteError": "<PERSON><PERSON><PERSON><PERSON>ada<PERSON>", "postcodeValidation": "Poskod mesti tepat 5 digit", "postcodeHelper": "Poskod mesti tepat 5 digit", "masukkan5DigitPoskod": "Masukkan 5 digit Poskod", "passwordPattern": "<PERSON>a la<PERSON>an perlu: 8 aksara minimum, huruf besar & kecil, nombor dan aksara khas", "passwordRequirements": "Masukkan sekurang-kurangnya 8 aksara dengan gabungan huruf besar & k<PERSON><PERSON>, nombor, dan aksara khas (@$!%*#?&)", "setiapTahun": "<PERSON><PERSON><PERSON>", "duaTahunSekali": "<PERSON><PERSON> (2) tahun sekali", "bilangan": "Bilangan", "bilanganAhliJawatanKuasa": "Bilangan Ahli J<PERSON>kuasa", "singkatanNamaRequired": "<PERSON>la masukkan singkatan nama", "takrifNamaRequired": "<PERSON><PERSON> masukkan takrifan nama", "kawasan": "<PERSON><PERSON><PERSON>", "kampung": "Kampung", "mukim": "Mukim", "tamanPerumahan": "<PERSON><PERSON>", "messageKeputusanPermohonanSuccess": "Keputusan permohonan induk berjaya di<PERSON>tar", "messageKeputusanPermohonanError": "Keputusan permohonan induk gagal dihantar", "bantuanKelahiranAnakPertama": "<PERSON><PERSON><PERSON>", "bantuanKelahiranAnakKedua": "<PERSON><PERSON><PERSON>", "bantuanKelahiranAnakKetiga": "<PERSON><PERSON><PERSON>", "bantuanKelahiranAnakSeterusnya": "<PERSON><PERSON><PERSON>", "bantuanKematianAhliSendiri": "<PERSON><PERSON><PERSON>", "bantuanKematianPasanganAhli": "<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>", "bantuanKematianAnakAhli": "<PERSON><PERSON><PERSON>", "bantuanKematianIbuBapaAhli": "<PERSON><PERSON><PERSON>/Bapa <PERSON>", "bantuanKematianNenekDatukAhli": "<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>", "bantuanPengebumian": "Bantuan <PERSON>", "bantuanNafkahAhliKeuzuran": "Bantu<PERSON>/<PERSON><PERSON><PERSON><PERSON> yang <PERSON>", "bantuanNafkahAhliBalu": "Bantuan/<PERSON><PERSON><PERSON>h <PERSON> yang <PERSON>", "bantuanNafkahAhliBaluAnak": "Bantuan/Nafkah Ah<PERSON> Ba<PERSON> yang Mempunyai Anak-anak <PERSON>h Umur 12 Tahun", "bilanganOrangPemegangAmanah": "Bilangan Orang Pemegang Amanah", "rekodPertubuhan": "<PERSON><PERSON><PERSON>", "idAlreadyRegistered": "Ralat : No. Pengenalan Diri telah berdaftar di sistem eRoses. Sila log masuk.", "icValidationOff": "Sistem kami sedang di<PERSON>, <PERSON>la cuba sebentar lagi.", "invalidIdNumber": "Ralat : No. Pengenalan Diri tidak sah", "errorValidatingId": "<PERSON><PERSON>", "otpSendToEmail": "OTP telah dihantar ke e-mel {{email}}. <PERSON>la masukkan kod 6 digit untuk pengesahan.", "enterOtp": "Masukkan OTP", "belumTerimEmel": "Belum terima emel?", "hantarSemula": "<PERSON><PERSON>", "otpSentMessage": "OTP telah dihantar ke emel anda. Sila semak emel dan set semula kata laluan baru untuk meneruskan proses", "infoQaSatu": "<PERSON><PERSON><PERSON><PERSON> pendaftaran pertubuhan anda akan disimpan selama 30 hari dari tarikh mula mohon.", "jenisPerlembagaanPopover": "Perlembagaan hendaklah mengikut format yang disediakan oleh JPPM", "semakFasal": "Se<PERSON><PERSON>", "isi": "<PERSON><PERSON>", "taraf": "<PERSON><PERSON>", "nameDefinitionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> nama pertubuhan ini membawa maksud.............", "bayaranMasuk": "Bayaran <PERSON>", "permulaanTahunKewangan": "<PERSON><PERSON><PERSON><PERSON>", "jenisMesyuaratAgungDanTempohLantikan": "<PERSON><PERSON>n <PERSON>", "masaDanTarikhMesyuarat": "<PERSON><PERSON> dan <PERSON>", "kehadiranAhliMesyuarat": "<PERSON><PERSON><PERSON><PERSON>", "jumlahKehadiranMesyuarat": "<PERSON><PERSON><PERSON>", "kehadiranAjk": "Ke<PERSON>iran AJK", "branchMeeting": "Mesyuarat <PERSON>n", "tambahMesyuarat": "<PERSON><PERSON>ua<PERSON>", "deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "peringatan": "PERINGATAN", "lanjutMasaWarning": "<PERSON><PERSON>a medan bertanda * mesti diisi. Lanjutan masa dibenarkan sekali sahaja.", "permintaanSertaPertubuhan": "<PERSON><PERSON><PERSON><PERSON>", "jumlahAhliPertubuhan": "<PERSON><PERSON><PERSON>", "senaraiAhliPertubuhan": "<PERSON><PERSON><PERSON>", "maklumatPeribadiAhli": "Maklumat Per<PERSON> Ah<PERSON>", "peringatanGunakanAlamatSah": "<PERSON>la gunakan alamat yang sah bagi mengelakkan permohonan kebenaran penubuhan cawangan ini ditolak.", "petaMesyuarat": "<PERSON><PERSON>", "keterangan": "Keterangan", "direkodOleh": "<PERSON><PERSON><PERSON>", "bilanganAhliJawatankuasaTerkini": "Bilangan Ahli Jawatankuasa Terkini", "maklumatMesyuaratPelantikanAjk": "Maklumat Mesyuarat Pelantikan AJK", "bilanganAhliYangHadir": "Bilangan <PERSON>", "bilanganAhliJawatankuasaBukanWnTerkini": "Bilangan Ahli Jawatankuasa Bukan Warganegara Terkini", "senaraiAjkBukanWn": "Senarai AJK Bukan Warganegara", "tarikhLantik": "<PERSON><PERSON><PERSON>", "bilanganPemegangAmanahTerkini": "Bilangan <PERSON>", "bilanganJuruauditTerkini": "Bilanga<PERSON>", "pilihanJuruaudit": "<PERSON><PERSON><PERSON>", "namaAhliPertubuhan": "<PERSON><PERSON>", "maklumatJuruauditDalaman": "<PERSON><PERSON><PERSON><PERSON>", "maklumatJuruauditBertauliah": "Ma<PERSON><PERSON><PERSON>", "nomborLesen": "<PERSON><PERSON><PERSON>", "pilihanPegawaiAwam": "<PERSON><PERSON><PERSON>", "jenisPegawai": "<PERSON><PERSON>", "jabatanPendaftaran": "Jabatan <PERSON>ftaran", "pertubuhanMalaysia": "<PERSON><PERSON><PERSON>an Malaysia", "ahli": "<PERSON><PERSON>", "hubungi": "Hubung<PERSON>", "logMasukAhli": "Log Ma<PERSON>k", "terokaiBacaanTerkini": "Sorotan Bacaan untuk Anda", "penyampaianPerkhidmatan": "Sistem eROSES versi 2.0", "daftarAkauneRoses": "<PERSON><PERSON><PERSON> e<PERSON>", "latihan": "<PERSON><PERSON><PERSON>", "takwim": "Takwim", "semakanJPPM": "Semakan JPPM", "hebahan": "<PERSON><PERSON><PERSON>", "COGG": "COGG", "FAQ": "FAQ", "panduanPengurusanPertubuhan": "<PERSON><PERSON><PERSON><PERSON>", "bacaanTerkiniUntukAnda.": "Bacaan Terkini Untuk Anda.", "pertubuhan": "<PERSON><PERSON><PERSON><PERSON>", "penarafanKepadaPertubuhan.": "<PERSON><PERSON><PERSON>.", "mediaSosial.": "Media Sosial.", "agensiKawalSeliaPertubuhanSelainJPPM": "<PERSON><PERSON><PERSON>", "pengurusanPerlembagaan": "<PERSON><PERSON><PERSON><PERSON>", "perlembagaanPertubuhan": "<PERSON><PERSON><PERSON><PERSON>", "permohonanPindaanPerlembagaan": "<PERSON><PERSON><PERSON><PERSON>", "maklumatMesyuaratPindaanPerlembagaan": "Maklumat Mesyuarat <PERSON>", "maklumatPindaanPerlembagaan": "Maklumat Pi<PERSON>an <PERSON>", "bercawangan": "<PERSON><PERSON><PERSON><PERSON>", "permohonanMigrasiPerlembagaan": "<PERSON><PERSON><PERSON><PERSON>", "mandatoryFieldsAlert": "<PERSON>la lengkapkan semua medan yang bertanda", "dissolutionMeetingInformation": "Maklumat Mesyuarat Pembubaran", "currentAssets": "<PERSON><PERSON>", "assetInformationList": "<PERSON><PERSON><PERSON>", "assetInformationRegister": "Daftar Maklumat Aset", "assetType": "<PERSON><PERSON>", "solution": "<PERSON>", "username": "<PERSON><PERSON>", "userStatus": "Status Pengguna", "jpnStatus": "Status JPN", "legal": "Sah", "illegal": "Tidak Sah", "pembayaranOnline": "Pembayaran Online", "featured": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "member": "<PERSON><PERSON>", "notMember": "<PERSON><PERSON><PERSON>", "registerAsAMember": "Daftar Se<PERSON>", "corporate": "Korporat", "newsHighlights": "Berita & Sorotan", "businessPartners": "<PERSON><PERSON>", "connectWithUs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scamAlerts": "Penipuan & Makluman", "procurement": "<PERSON><PERSON><PERSON>", "termsConditions": "Terma & Syarat", "securityPolicy": "<PERSON><PERSON>", "disclaimer": "<PERSON><PERSON><PERSON>", "onlineSecurityTips": "<PERSON><PERSON>", "otherLinks": "<PERSON><PERSON><PERSON>", "forms": "<PERSON><PERSON>", "resourceCentre": "Pusat Sumber", "achievements": "Pencapaian", "tarikhKeputusanDitolak": "<PERSON><PERSON><PERSON> kep<PERSON>n di<PERSON>lak", "nomborTelefonRumah": "Nombor Telefon Rumah", "nomborTelefonPejabat": "Nombor telefon pejabat", "lihatSenaraiSemak": "<PERSON><PERSON>", "clickToUpload": "Klik untuk memuat naik", "uploadComplete": "<PERSON>at naik se<PERSON>ai", "senaraiSemakRayuan": "<PERSON><PERSON><PERSON> semak rayuan", "senaraiSemakRayuanContent": "1. Surat Permohonan <PERSON>\n\n2. Salinan Resit Bayaran Fi Berjumlah RM 50.00\n\n3. Salinan Notis Perintah Mengemukakan Maklumat (Notis Seksyen 14/2)\n\n4. Salinan Notis Pertubuhan Sebelum Dibatalkan (Notis Seksyen 13/2/ Seksyen 13(1)(a)/ Subseksyen 14(5)/ Lain-lain)\n\n5. Salinan Si<PERSON>l <PERSON>tub<PERSON> (Borang 3)\n\n6. Penyata <PERSON> Lengkap Mengikut Tahun Sepertimana Tertera Dalam Notis Seksyen 13(2) dan Seksyen 14(2)\n\n\b  \b  \b  \b  \b a) Senarai Ahli Jawatankuasa\n\b  \b  \b  \b  \b b) Minit Mesyuarat\n\b  \b  \b  \b  \b c) Penyata Kewangan\n\nLain-lain dokumen yang dikemukakan adalah DIWAJIBKAN MENGGUNAKAN Bahasa Melayu selaras dengan Seksyen 2A (1)(c), <PERSON><PERSON><PERSON> 1966.", "langkahPermohonanRayuan": "<PERSON><PERSON><PERSON>", "bayaran": "Bayaran", "filterBy": "<PERSON><PERSON>", "uploading": "<PERSON><PERSON><PERSON> naik", "pengerusi": "<PERSON><PERSON><PERSON><PERSON>", "setiausaha": "<PERSON><PERSON><PERSON><PERSON>", "penolongSetiausaha": "<PERSON><PERSON><PERSON>", "penolongSetiausahaCawangan": "Penolong Set<PERSON>", "bendahari": "<PERSON><PERSON><PERSON>", "bendahariCawangan": "<PERSON><PERSON><PERSON>", "ahliJawatankuasa": "<PERSON><PERSON>", "penggunaBiasa": "Pengguna Biasa", "penolongBendahari": "Penolong <PERSON>", "penolongBendahariCawangan": "Penolong Bendahari Cawangan", "ahliBiasa": "<PERSON><PERSON>", "perwakilanNegeri": "Perwakilan Negeri", "lain-lain": "Lain-lain", "MyPR": "MyPR", "emptyField": "Medan tidak boleh kosong", "date": "<PERSON><PERSON><PERSON>", "nonCitizenVerifyErrorMsg": "No. Pengenalan Diri telah berdaftar di sistem eRoses. Sila log masuk.", "langkahCarianDokumen": "<PERSON><PERSON><PERSON>", "carianPertubuhanDanMaklumat": "<PERSON><PERSON> dan mak<PERSON>", "setujutext": "<PERSON><PERSON> dengan ini saya mengesahkan bahawa segala maklumat yang diberikan adalah benar. Jika pihak JABATAN PENDAFTARAN PERTUBUHAN mendapati berlaku penipuan dan kepalsuan dalam keterangan dokumen yang telah saya berikan di atas, maka pihak JABATAN PENDAFTARAN PERTUBUHAN adalah dengan ini berhak untuk menolak permohonan saya dan jika disabitkan bersalah, saya boleh dikenakan denda tidak melebihi denda RM 2000 mengikut seksyen 54 A, AKTA PERTUBUHAN 1966 serta mana-mana undang-undang yang berkuatkuasa.", "silaMasukNamaPertubuhan": "<PERSON>la masukkan nama pertubuhan dengan betul.", "dokumen": "Dokumen", "amaun": "<PERSON><PERSON><PERSON>", "jumlah": "JUMLAH", "seterusnya": "Seterusnya", "akuansetujuterima": "<PERSON><PERSON><PERSON> setuju terima.", "paymentmethod": "<PERSON>", "notaSilamembuatbayaranJppm": "NOTA: <PERSON><PERSON> membuat bayaran di kaunter JPPM yang berdekatan jika anda memilih cara pembayaran di kaunter.", "permohonanCarianDokumen": "<PERSON><PERSON><PERSON><PERSON>", "silaHadir": "<PERSON>la hadir ke JPPM berdekatan bagi carian maklumat Pertubuhan yang berdaftar sebelum:", "placeanddate": " 16/01/2012 (Perak, Selangor, Kuala Lumpur, Johor, Negeri Sembilan)", "palceanddate2": " 01/07/2012 (<PERSON><PERSON><PERSON>)", "carianDokumenLulusDalamTempoh": "Carian dokumen yang diluluskan perlu dimuat turun selama tempoh", "frompaydate": "dari tarikh p<PERSON>.", "30days": "30 hari", "sureToSubmit": "<PERSON><PERSON>h anda pasti untuk menghantar permohonan ini?", "back2": "Kembali", "feedbackHerotext": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>?", "cadanganMaklumbalasbaru": "<PERSON><PERSON>nga<PERSON> dan <PERSON> balas", "SemakCadangan": "<PERSON><PERSON><PERSON> dan <PERSON> balas", "cadanganMaklumbalas": "Cadangan dan <PERSON>", "aduan": "aduan", "cadanganOrMakluman": "Cadangan/Makluman", "pertanyaan": "<PERSON><PERSON><PERSON>", "jenis": "<PERSON><PERSON>", "tajuk": "Tajuk", "butiranText": "<PERSON><PERSON><PERSON>", "lokasi": "<PERSON><PERSON>", "jantina": "jantina", "lampiranCadanganDanMaklumbalas": "<PERSON><PERSON><PERSON> dan <PERSON>", "silaPilihJenis": "<PERSON><PERSON> pilih jenis", "tajukRequired": "Tajuk diperlukan", "lokasiRequired": "<PERSON><PERSON>an", "janitaRequired": "<PERSON><PERSON>", "negeriRequired": "<PERSON><PERSON><PERSON>", "daerahRequired": "<PERSON><PERSON><PERSON>", "poskodRequired": "poskod <PERSON>an", "bandarRequired": "bandar diperlukan", "jenisPengenalanDiriRequired": "<PERSON><PERSON> diri <PERSON>an", "nomborPengenalanDiriRequired": "Nombor pengenalan diri dip<PERSON>lukan", "perkerjaanRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "idType2": "<PERSON><PERSON>", "semak": "Semak", "jenispengenalandRequired": "<PERSON><PERSON>an", "uploadCompleted": "<PERSON>at naik se<PERSON>ai", "disahkanOleh": "<PERSON><PERSON><PERSON><PERSON>", "disediakanOleh": "<PERSON><PERSON><PERSON><PERSON>", "organizationSubCategory2": "Sub <PERSON><PERSON><PERSON>", "feedbackList": "<PERSON><PERSON><PERSON> ma<PERSON> balas", "jenisCadanganMaklumBalas": "<PERSON><PERSON> cadan<PERSON> / maklum balas", "tajukButiran": "Tajuk/Butiran", "feedback": "<PERSON><PERSON><PERSON> balas", "systemIssue": "isu sistem", "ExtraordinaryGeneralMeeting": "Mesyuarat Agung Luar Biasa / Khas", "feedBackSuccessSent": "<PERSON><PERSON><PERSON>a <PERSON>!", "noRujukanMaklumbalas": "Nombor Rujukan maklum balas", "silaNoSemak": "Sila simpan nombor rujukan ini. <PERSON>a boleh menyemak status maklum balas dengan memasukkan nombor rujukan tersebut.", "thankyou": "<PERSON><PERSON>", "AM": "<PERSON><PERSON>", "PM": "<PERSON><PERSON>", "administrasi": "Administrasi", "carian": "<PERSON><PERSON>", "perlembagaan": "Perlembagaan", "profil": "Profil", "petiMasuk": "<PERSON><PERSON> ma<PERSON>k", "keluar": "<PERSON><PERSON><PERSON>", "tetapan": "Tetapan", "tarikhPemohonan": "<PERSON><PERSON><PERSON>", "namaTempatMesyuarat": "Nama Tempat Mesyuarat", "buang": "<PERSON><PERSON>", "PERINGATAN": "PERINGATAN : <PERSON><PERSON> ahli jaw<PERSON> yang TIDAK BEKERJA/ PESARA, maklumat majikan tidak perlu diisi", "organizationNumber2": "<PERSON><PERSON><PERSON>", "negaraAsal": "<PERSON><PERSON><PERSON> asal", "nomborVisa": "Nombor Visa", "nomborPermit": "Nombor Permit", "tujuanDiMalaysia": "<PERSON><PERSON><PERSON>", "tempohDiMalaysia": "Tempoh di Malaysia", "importanceOfPosition2": "Jelas<PERSON> secara ringkas kepentingan bukan warganegara memegang jawatan", "daftarCawanganBaru": "Daftar Cawangan Baru", "daftarSetiausahaCawanganBaru": "Pembaharu<PERSON>", "dissolutionSteps": "<PERSON><PERSON><PERSON>", "financialInformation": "<PERSON><PERSON><PERSON><PERSON>", "applicationForDissolution": "<PERSON><PERSON><PERSON><PERSON>", "committeeReview": "<PERSON><PERSON><PERSON> jaw<PERSON>", "noData": "Tiada Data", "simpan": "simpan", "landing_text_1": "Terokai perkhidmatan yang ditawarkan dalam Sistem eROSES 2.0", "panduanPengurusanPertubuhan_subtext": "<PERSON><PERSON> panduan lengkap untuk pendaftaran pertubuhan, pengu<PERSON>an perlem<PERSON>, penghan<PERSON>n penyata tahunan, pengurusan jawatank<PERSON><PERSON> dan pengurusan pertubuhan.", "takwim_subtext": "Program dan aktiviti anjuran JPPM sepanjang tahun untuk pertubuhan.", "grant_subtext": "In<PERSON><PERSON> kera<PERSON>an untuk menyokong aktiviti dan pembangunan per<PERSON>uhan.", "cogg": "Code of Good Governance (COGG)", "cogg_subtext": "Panduan asas untuk tadbir urus pertubuhan yang telus dan berintegriti.", "latihan_subtext": "Aktiviti pembangunan untuk men<PERSON> kema<PERSON>, dan pen<PERSON><PERSON>an dalam pengu<PERSON>an per<PERSON>.", "semakan": "Semakan", "semakan_subtext": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan penilaian ter<PERSON><PERSON> pertubuh", "soalanlazim": "Soalan Lazim(FAQ)", "soalanlazim_subtext": "Dapatkan jawapan kepada soalan-soalan umum berkaitan perkhid<PERSON>n, prose<PERSON><PERSON>, dan maklumat penting yang sering ditanya untuk memudahkan urusan anda.", "KetahuiLanjut": "<PERSON><PERSON><PERSON>", "pegawaiAwamDeclaration": "<PERSON>a mengaku bahawa Pegawai Awam yang dilantik adalah daripada ahli persatuan. Sekiranya maklumat yang didapati palsu, adalah tertakluk kepada akta pertubuh<PERSON> 1996 dan Peraturan-peraturan 1984.", "PertubuhanSelainJPPM_text": "<PERSON><PERSON>-<PERSON><PERSON> pen<PERSON> lain yang bertanggungjawab memastikan organisasi di Malaysia mematuhi undang-undang dan peraturan yang ditetapkan.", "jppm": "Jabatan Pendaftaran Pertubuhan Malaysia.", "Perkhidmatan Teras": "Perkhidmatan Teras", "kisahText": "<PERSON><PERSON><PERSON> dan Inspirasi persatuan", "kisahText_1": "<PERSON><PERSON><PERSON>", "kisahText_2": "Akautabiliti teras masyarakat", "kisahText_3": "<PERSON><PERSON><PERSON><PERSON>", "kisahText_4": "<PERSON><PERSON><PERSON> ma<PERSON><PERSON><PERSON>,", "kisahText_5": "Masyarakat be<PERSON>", "kisahText_6": "Persatuan Menuru<PERSON>", "agensiJppm_text": "<PERSON><PERSON><PERSON> selia <PERSON>", "bacaantitle": "Inspirasi <PERSON>", "bacaann_subtitle": "<PERSON><PERSON><PERSON><PERSON> akan dinilai berdasarkan tahap kepatuhan pertubuhan terhadap undang-undang dan peraturan yang ditetapkan serta keberkesanan aktiviti yang dijalankan.", "exploreMore": "<PERSON><PERSON><PERSON> lebih lan<PERSON>t", "meetingName2": "Nama tempat mesyuarat", "tempohMuatTurun": "Tempoh Mu<PERSON>", "disahkanOleh ": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> ", "noppm": "No. PPM", "jawatan": "Jawatan", "chairmanPersonalInfo": "Maklumat Peribadi", "keputusanInduk": "Keputusan Induk", "lihatpaparanpenuh": "<PERSON><PERSON> p<PERSON>ran <PERSON>uh", "belumselesai": "<PERSON><PERSON>", "sureAjk": "Sila pastikan maklumat AJK lengkap", "kliksemak": "KLIK UNTUK SEMAK", "namaDisenaraiKelabu": "<PERSON>a per<PERSON>an terdapat di senarai Kelabu", "tandaJijaSamaDiatas": "Tanda jika sama diatas", "ajk_jawatankuasa_peringatan_1_point1": "Sila pastikan bilangan Ahli Jawatankuasa Biasa mengikut bilangan di dalam perlembagaan.", "ajk_jawatankuasa_peringatan_1_point2": "Bagi pembaharuan setia<PERSON>ha sila klik <1>di sini</1> atau pada menu <2>Dashboard > Pembaharuan Setia<PERSON><2>.", "ajk_jawatankuasa_peringatan_1_point3": "Pengemaskinian maklumat AJK memerlukan anda untuk mengisi maklumat mesyuarat pelantikan AJK yang baru. Jika Senarai Mesyuarat tiada dalam pilihan, klik <1>di sini</1> bagi kemasukan maklumat mesyuarat terlibat.", "ajk_jawatankuasa_peringatan_1_point4": "Perubahan maklumat AJK akan dikemaskini setelah anda membuat pengesahan di bahagian terakhir dan menekan butang “Kemaskini”.", "bilanganPemegangJawatan": "Bilangan Pemegang Jawatan", "ajk_jawatankuasa_peringatan_2_point1": "Bahagian ini adalah untuk mendaftarkan AJK yang bukan warganegara.", "ajk_jawatankuasa_peringatan_2_point2": "Pegang J<PERSON>tan bagi bukan warganegara ini perlu mendapat kebenaran dari Pendaftar Pertubuhan terlebih dahulu dengan menekan butang “Hantar”.", "ajk_jawatankuasa_peringatan_2_point3": "<PERSON><PERSON> per<PERSON> keb<PERSON>ran bukan warganegara telah <PERSON>, AJK bukan warganegara akan tersenarai dalam Senarai AJK.", "ketetapanPiagam": "<PERSON><PERSON><PERSON><PERSON>", "pengurusanIntegrasi": "<PERSON><PERSON><PERSON><PERSON>", "carianAuditTrail": "Carian audit trail", "cari": "<PERSON><PERSON>", "tiadaData": "Tiada data", "tarikhDanMasaRekod": "<PERSON><PERSON><PERSON> dan masa rekod", "noPengenalanDiriDisemak": "No. Pengenalan Diri disemak", "modul": "<PERSON><PERSON><PERSON>", "langkahPenyataTahunan": "<PERSON><PERSON><PERSON> pen<PERSON>ta ta<PERSON>an", "penyataPendapatanPerbelanjaan": "<PERSON><PERSON><PERSON>", "paparan": "<PERSON><PERSON>", "tahunPenyata": "<PERSON><PERSON>", "tambahPenyataTahunan": "Tambah Penya<PERSON>", "tiadaMaklumat": "Tiada Ma<PERSON>lumat", "penyataTahunan_peringatan_point": "Sila isi maklumat mesyuarat pembentangan penyata tahunan jika <PERSON> tiada dalam pilihan atau <1>klik di sini</1> bagi kemasukan maklumat mesyuarat terlibat.", "maklumatMesyuaratPenyataTahunan": "<PERSON><PERSON><PERSON><PERSON>", "penyataTahunan_ajk_peringatan_point_1": "Sila pastikan bilangan Ahli Jawatankuasa Biasa mengikut bilangan di dalam perlembagaan.", "penyataTahunan_ajk_peringatan_point_2": "<PERSON>gi kemaskini AJK <1>sila klik di sini</1> atau pada AJK & Keahlian > Jawatankuasa", "penyataTahunan_ajk_peringatan_point_3": "<PERSON>gi kemaskini pembaharuan setia<PERSON> <1>sila klik di sini</1> atau pada menu Dashboard > Pembaharuan Setia<PERSON>.", "penyataTahunan_ajk_peringatan_point_cawangan_3": "<PERSON>gi kemaskini pembaharuan setia<PERSON> <1>sila klik di sini</1> atau pada menu Dashboard > Pembaharuan Setia<PERSON>awangan.", "kemaskiniAJK": "Kemaskini AJK", "peyataTahunan_pendapatan_pengakuan": "Dengan ini saya mengaku bahawa persatuan ini tidak mempunyai sebarang Pendapatan & Perbelanjaan pada tahun kewangan ini.", "lainLainPendapatan": "Lain lain pendapatan", "jumlahPerbelanjaan": "<PERSON><PERSON><PERSON>", "daftarSumbanganDariLuarNegara": "Daftar sumbangan dari luar negara", "daftarSumbanganKeLuarNegara": "Daftar sumbangan ke luar negara", "listOfContributionsFromInbroad": "Sen<PERSON>i sumbangan ke luar negara", "contributionToInbroadInformation": "Maklumat sumbangan dari luar negara", "penerimaSumbangan": "<PERSON><PERSON><PERSON>", "muatTurunPenyataTahunan": "<PERSON>at turun penyata tahunan", "akuan": "<PERSON><PERSON><PERSON>", "pengakuan_description_1": "<PERSON><PERSON> dengan ini saya mengesahkan bahawa segala maklumat yang diberikan adalah benar. Jika pihak Jabatan Pendaftaran Pertubuhan Malaysia mendapati berlaku penipuan dan kepalsuan dalam keterangan dokumen yang telah saya berikan di atas, saya boleh dikenakan denda tidak melebihi denda RM2000 mengikut Seksyen 54A, <PERSON><PERSON><PERSON> 1966", "pengakuan_description_2": "ii. <PERSON><PERSON> dalam tempoh memegang jawatannya adalah bertang<PERSON> ke atas semua laporan dan maklumat semasa memegang jawatan.", "selenggara": "Selenggara", "semakanDanCarian": "<PERSON><PERSON><PERSON> dan <PERSON>", "semakanDanCarian_subtext": "Semakan maklumat berkaitan pertubuhan termasuk status semasa dan rekod senarai hitam.", "penarafanPertubuhan": "<PERSON><PERSON><PERSON>", "penarafanPertubuhan_subtext": "JPPM menilai tahap kepatuhan pertubuhan terhadap undang-undang dan peraturan yang diteta<PERSON>, serta keberkesanan aktiviti yang dijalankan.", "searchnoFoundOrga": "<PERSON><PERSON><PERSON>an tiada dalam rekod. Sila hubungi JPPM Negeri untuk menyemak status pertubuhan anda", "informationOnDissolutionOrganizations": "Maklumat Pembubaran <PERSON>", "branchSecretary": "<PERSON><PERSON><PERSON><PERSON>", "aduanCadangan": "Aduan/Cadangan", "kepuasanPelanggan": "Kepuasan pelanggan", "faqMakluman": "FAQ/Makluman", "noPengenalanDiriPemohon": "No. Pengenalan Diri <PERSON>", "noPengenalanDiriPembayar": "No. pengenalan Diri <PERSON>", "jenisPembayaran": "<PERSON><PERSON>", "butiranPembayaranKaunter": "<PERSON><PERSON><PERSON>", "noResit1": "No Resit", "pembayaranKaunterDialogMsg1": "<PERSON><PERSON>h anda pasti No. Resit yang dimasukkan betul?", "pembayaranKaunterDialogMsg2": "<PERSON><PERSON><PERSON> p<PERSON> ber<PERSON>", "negeriTerimaBayaran": "Negeri terima bayaran", "carianAduanCadangan": "Carian <PERSON>/Cadangan", "tarikhTerimaAduan": "<PERSON><PERSON><PERSON> terima aduan", "muatTurunLaporanAduanCadangan": "Muat turun laporan <PERSON>/Cadangan", "senaraiAduanCadangan": "Senarai <PERSON>/Cadangan", "tarikhAduanDiterima": "<PERSON><PERSON><PERSON> aduan di<PERSON>", "tempohAduanDiterimaHari": "Tempoh aduan di<PERSON> (hari)", "mula": "<PERSON><PERSON>", "akhir": "<PERSON><PERSON><PERSON>", "maklumatAduanCadangan": "Maklumat Aduan/Cadangan", "tarikhDanMasaAduanDihantar": "<PERSON><PERSON><PERSON> dan masa aduan di<PERSON>tar", "lampiran": "Lam<PERSON>ran", "maklumatPenghantar": "Maklumat Penghantar", "cawanganJPPM": "Cawangan JPPM", "sejarahAduanCadangan": "<PERSON><PERSON><PERSON>/Cadangan", "auditTrailaduan": "Audit trail aduan", "paparSejarahAduanCadangan": "<PERSON>r sejarah aduan/cadangan", "keputusanAduanCadangan": "Keputusan Aduan/Cadangan", "penerimaAduan": "<PERSON><PERSON><PERSON>", "negeriMenerimaAduan": "<PERSON><PERSON><PERSON>", "tindakanPegawai": "Tindak<PERSON> pegawai", "tahapAduan": "<PERSON><PERSON><PERSON>", "catatanPegawaiCaraPengyelesaian": "Catatan <PERSON> (Cara Penyelesaian)", "aduanCadanganDialogMsg1": "<PERSON><PERSON>h anda pasti dengan keputusan aduan ini?", "jumlahMaklumBalasKepuasanPelanggan": "<PERSON><PERSON><PERSON> ma<PERSON> balas kepuasan pelanggan", "total": "<PERSON><PERSON><PERSON>", "senaraiMaklumBalasKepuasanPelanggan": "<PERSON><PERSON><PERSON> mak<PERSON> balas kepuasan pelanggan", "jenisMakluman": "<PERSON><PERSON>", "senaraiFAQ_Makluman": "Senarai FAQ/Makluman", "kandungan": "Kandungan", "tambahFAQ_Makluman": "Tambah FAQ/Makluman", "penambahanFAQ_Makluman": "Penambahan FAQ/Makluman", "kategoriPengguna": "<PERSON><PERSON>", "kategoriSoalan": "<PERSON><PERSON><PERSON>", "soalan": "Soalan", "jawapan": "<PERSON><PERSON><PERSON>", "tajukMaklumanPengumuman": "Tajuk ma<PERSON>/pengumuman", "maklumanPengumuman": "Makluman/Pengumuman", "tarikhMulaMaklumanPengumuman": "<PERSON><PERSON><PERSON> mula ma<PERSON>/pengum<PERSON>", "tarikhAkhirMaklumanPengumuman": "<PERSON><PERSON><PERSON> a<PERSON>/pen<PERSON><PERSON>", "secretaryName": "<PERSON><PERSON>", "secretaryPhone": "No Telefon Setiausaha (HP/Rumah/Pejabat)", "numberResponsesReceived": "Bilangan maklum balas yang diterima", "pegawai": "Pegawai", "feedbackDialog_title": "Log keluar berjaya !", "feedbackDialog_subtext1": "Terima kasih kerana telah menggunakan sistem eROSES.", "feedbackDialog_subtext2": "<PERSON><PERSON><PERSON> pen<PERSON> anda.", "feedbackDialog_subtext3": "<PERSON><PERSON> kasih atas maklum balas anda!", "tidakMemuaskan": "Tidak memuaskan", "memuaskan": "<PERSON><PERSON><PERSON><PERSON>", "sangatMemuaskan": "Sangat memuaskan", "stateOrganization": "<PERSON><PERSON><PERSON>", "nonCitizenSecretaryInformation": "Maklumat set<PERSON> bukan wargan<PERSON>ara", "daftarPenggunaJPPM": "Daftar Pengguna JPPM", "telephoneNoHome": "No Telefon (Rumah)", "telephoneNoMobile": "No Telefon (Bimbit)", "externalUserupdate": "Kemaskini Pengguna Luar", "userCannotActivateEmail": "<PERSON><PERSON> Masih <PERSON>um Dapat Mengaktifkan A<PERSON>un, <PERSON><PERSON> <b><PERSON><PERSON> Emel dan Hantar Menggunakan Akaun EMEL Tuan/Puan <PERSON>.</b>", "clickUpdateToResend": "<PERSON><PERSON> <b>KEMASKINI</b> jika ingin <b>men<PERSON><PERSON> semula</b> EMEL <b>melalui SISTEM eROSES.</b>", "pendaftaranAkauneROSES": "Pendaftaran Akaun eROSES", "pengaktifanAkauneROSES": "Pengaktifan Akaun eROSES", "pendaftaranAkauneROSESDesc": "<PERSON><PERSON> di<PERSON><PERSON>aki <PERSON>kar kata laluan sementara yang diberikan dengan kata laluan anda sendiri.", "thankYouForRegistering": "Terima kasih kerana mendaftar dengan Sistem e-ROSES. Dengan mendaftar untuk menggunakan sistem ini, anda akan dikategorikan sebagai Pengguna dan seterusnya bersetuju untuk terikat dengan Terma penggunaan ini. Sekiranya anda tidak bersetuju dengan Terma penggunaan ini dan tidak mahu terikat dengannya, anda dinasihatkan supaya tidak menggunakan sistem ini.", "thisEmailIsComputerGenerated": "Emel ini adalah janaan komputer dan tidak perlu dibalas. Sebarang pertanyaan sila hubungi Jabatan Pendaftaran Pertubuhan Negeri berdekatan.", "kandunganEMEL": "Kandungan EMEL", "idNumberOnlyDigits": "Mestilah 12 digit panjang", "userExists": "Id ini telah diberikan kepada pengguna sedia ada", "senaraiPenggunaMenungguKelulusan": "Senarai Pengguna JPPM menunggu kelulusan", "senaraiPenggunaJPPM": "Senarai pengguna JPPM", "senaraiPenggunaLuar": "Sen<PERSON>i pengguna luar", "jenis_MaklumBalas": "<PERSON><PERSON><PERSON>", "jenis_Aduan": "<PERSON><PERSON>", "jenis_IsuSistem": "<PERSON><PERSON>", "jenis_KepuasanPelangan": " Kepuasan <PERSON>ng<PERSON>", "status_New": "<PERSON><PERSON>", "status_InProgress": "Sedang Be<PERSON>lan", "status_Complete": "Lengka<PERSON>", "status_PendingIT": "Menunggu IT", "status_Close": "<PERSON><PERSON><PERSON>", "status_Reopen": "<PERSON><PERSON>", "officerName": "<PERSON><PERSON>", "nameOfBranchOrganization": "<PERSON><PERSON>", "numberOfBranches": "<PERSON><PERSON><PERSON>", "branchReferenceNo": "Nombor rujukan ca<PERSON>an", "branchNameDetails": "<PERSON><PERSON>", "registrationDetails": "<PERSON><PERSON> penda<PERSON>", "tempohMasaMesyuaratDanAJK": "<PERSON><PERSON><PERSON> masa (mesyuarat dan AJK)", "approval_waiting_wlist": "<PERSON><PERSON><PERSON> kel<PERSON>an induk", "propertyOfficerConfirmation": "Pengesahan Pegawai Hart<PERSON>", "publicOfficialVerification": "Pengesahan Pegawai Awam", "officePhoneNumber": "Nombor Telefon Pejabat", "homePhoneNumber": "Nombor Telefon Rumah", "senaraiDokumen": "Senarai dokumen", "keputusanCawangan_penubuhanMenungguPendaftaranCawangan": "<PERSON><PERSON><PERSON><PERSON>awa<PERSON>", "keputusanCawangan_seneraiPenubuhanMenungguPendaftaranCawangan": "<PERSON><PERSON><PERSON> Cawangan", "keputusanCawangan_namaPermohonan": "<PERSON><PERSON>", "keputusanCawangan_RO": "RO", "keputusanCawangan_MaklumatPermohonanPenubuhanCawangan": "Maklumat <PERSON>", "namePertubuhanInduk": "<PERSON><PERSON> induk", "ppmCawangan": "PPM Cawangan", "tarikhPermohonanLanjutMasa": "<PERSON><PERSON><PERSON>", "maklumatPermohonanLanjutMasa": "Maklumat <PERSON>", "permohonanMenungguKeputusan": "<PERSON><PERSON><PERSON><PERSON>", "senaraiMenungguKeputusanPindaanNamaDanAlamat": "<PERSON><PERSON><PERSON>gu keputusan pindaan nama dan alamat", "maklumatAsalCawangan": "Maklumat Asal Cawangan", "namaAsal": "<PERSON><PERSON>", "alamatAsal": "<PERSON><PERSON><PERSON>", "maklumatPindaNamaDanAlamatCawangan": "Maklumat Pinda Nama Dan <PERSON>", "kodPengenalan": "<PERSON><PERSON>", "permohonanPegawaiAwamCawanganMenunggiKelulusan": "<PERSON><PERSON><PERSON><PERSON> (Menung<PERSON>)", "pengesahanPegawaiAwamCawangan": "Pengesahan Pegawai Awam <PERSON>", "permohonanPegawaiHartaCawanganMenunggiKelulusan": "<PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "pengesahanPegawaiHartaCawangan": "Pengesahan Pegawai Harta Cawangan", "permohonanPembubaranCawangan": "<PERSON><PERSON><PERSON><PERSON>", "maklumatAset": "Maklumat Aset", "screenList": "<PERSON><PERSON><PERSON>", "read": "Baca", "createAdd": "Cipta/Tambah", "recordedBy": "Direkodkan oleh", "JPPMUserCategoryList": "<PERSON><PERSON><PERSON> JPPM", "ExternalUserCategoryList": "Sen<PERSON>i <PERSON>", "externalUserCategory": "<PERSON><PERSON><PERSON>", "addCategory": "Tambah Peranan", "emailQueue": "<PERSON><PERSON>", "bahagian": "Bahagian", "gred": "<PERSON><PERSON>", "negara": "Negara", "agama": "<PERSON><PERSON>a", "keturunan": "Keturunan", "jabatanInsolvensi": "Jabatan Insolvensi", "kalendar": "<PERSON><PERSON><PERSON>", "namaBahagian": "<PERSON><PERSON> b<PERSON>", "senaraiBahagian": "<PERSON><PERSON><PERSON> bah<PERSON>", "kodBahagian": "<PERSON><PERSON> b<PERSON>", "tambahBahagian": "<PERSON><PERSON> bahagian", "aktiviti": "Activiti", "penambahanBandar": "<PERSON><PERSON><PERSON><PERSON>", "wujudDaftar": "<PERSON><PERSON><PERSON> da<PERSON>", "kemaskiniTerakhir": "<PERSON><PERSON><PERSON> terakhir", "namaJawatan": "<PERSON><PERSON> jaw<PERSON>n", "gredJawatan": "<PERSON><PERSON>n", "senaraiJawatan": "<PERSON><PERSON><PERSON> jawatan", "penambahanJawatan": "<PERSON><PERSON><PERSON><PERSON>", "kemaskiniJawatan": "Kemaskini Jawatan", "keteranganJawatan": "Keterangan Jawatan", "namaGred": "<PERSON><PERSON> gred", "kodGred": "<PERSON>d gred", "keteranganGred": "Keterangan Gred", "senaraiGred": "<PERSON><PERSON><PERSON>", "tambahGred": "Tambah Gred", "penambahanGred": "<PERSON><PERSON><PERSON><PERSON>", "kemaskiniGred": "Kemaskini Gred", "namaPekerjaan": "<PERSON><PERSON>", "kodPekerjaan": "<PERSON><PERSON>", "keteranganPekerjaan": "Keterangan pek<PERSON>", "senaraiPekerjaan": "<PERSON><PERSON><PERSON>", "tambahPekerjaan": "<PERSON><PERSON> pek<PERSON>", "penambahanPekerjaan": "<PERSON><PERSON><PERSON><PERSON>", "kemaskiniPekerjaan": "<PERSON><PERSON><PERSON>", "namaNegara": "<PERSON><PERSON> negara", "kodNegara": "Kod negara", "shortKodNegara": "<PERSON><PERSON>", "senaraiNegara": "<PERSON><PERSON><PERSON> negara", "tambahNegara": "Tambah negara", "penambahanNegara": "Penambahan negara", "kemaskiniNegara": "Kemaskini negara", "namaNegeri": "<PERSON><PERSON> negeri", "kodNegeri": "<PERSON><PERSON> negeri", "shortKodNegeri": "<PERSON><PERSON>", "senaraiNegeri": "<PERSON><PERSON><PERSON> negeri", "tambahNegeri": "Tambah negeri", "penambahanNegeri": "<PERSON><PERSON><PERSON><PERSON> negeri", "kemaskiniNegeri": "Ke<PERSON><PERSON> negeri", "namaDaerah": "<PERSON><PERSON> da<PERSON>h", "kodDaerah": "<PERSON><PERSON> da<PERSON>h", "shortKodDaerah": "<PERSON><PERSON> pendek daerah", "senaraiDaerah": "<PERSON><PERSON><PERSON> da<PERSON>h", "tambahDaerah": "Tambah daerah", "penambahanDaerah": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>h", "kemaskiniDaerah": "<PERSON><PERSON><PERSON> da<PERSON>h", "namaBandar": "<PERSON><PERSON> bandar", "kodBandar": "Kod bandar", "senaraiBandar": "<PERSON><PERSON><PERSON> bandar", "tambahBandar": "Tambah bandar", "kemaskiniBandar": "Kemaskini bandar", "namaAgama": "<PERSON>a agama", "kodAgama": "<PERSON><PERSON> agama", "senaraiAgama": "<PERSON><PERSON><PERSON> agama", "tambahAgama": "Tambah agama", "kemaskiniAgama": "<PERSON><PERSON><PERSON> agama", "namaKeturunan": "<PERSON><PERSON> k<PERSON>", "kodKeturunan": "<PERSON>d ket<PERSON>", "senaraiKeturunan": "<PERSON><PERSON><PERSON> k<PERSON>", "tambahKeturunan": "Tambah keturunan", "kemaskiniKeturunan": "Kemaskini keturunan", "tarikhDiterima": "<PERSON><PERSON><PERSON>", "tarikhDihantar": "<PERSON><PERSON><PERSON>", "jenisCuti": "<PERSON><PERSON>i", "namaCuti": "<PERSON><PERSON> cuti", "namaJabatanInsolvensi": "Nama Jabatan Insolvensi", "tambahJabatanInsolvensi": "Tambah Jabatan Insolvensi", "kodInsolvensi": "<PERSON><PERSON>", "penambahanJabatanInsolvensi": "Penambahan Jabatan Insolvensi", "kemaskiniJabatanInsolvensi": "Kemaskini Jabatan Insolvensi", "kodCawangan": "<PERSON><PERSON>", "penambahanCawanganJPPM": "<PERSON><PERSON><PERSON><PERSON>ngan JPPM", "kemaskiniCawanganJPPM": "Kemaskini Cawangan JPPM", "tambahCawanganJPPM": "Tambah Cawangan JPPM", "historyInquiry": "<PERSON><PERSON><PERSON>", "secretaryInformation": "<PERSON><PERSON><PERSON><PERSON>", "secretaryAddress": "<PERSON><PERSON><PERSON>", "reasonForSecretaryChange": "<PERSON><PERSON><PERSON>", "otherReason": "<PERSON><PERSON><PERSON> lain", "pendaftaranPertubuhanInduk": "Pendaftaran pertubuhan induk", "pendaftaranCawangan": "Pendaftaran cawangan", "pindaanUndangUndangInduk": "<PERSON><PERSON><PERSON>", "senaraiKuiri": "<PERSON><PERSON><PERSON>", "kategoriPertubuhan": "<PERSON><PERSON><PERSON>", "kuiriPendaftaranPertubuhanInduk": "<PERSON><PERSON>an <PERSON>", "pemohon": "<PERSON><PERSON><PERSON><PERSON>", "noPertubuhan": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "kuiriPendaftaranCawangan": "Kuiri Pendaftaran Cawangan", "kuiriPindaanUndangUndangInduk": "<PERSON><PERSON>", "kuiriRayuan": "<PERSON><PERSON>", "kuiriPembaharuanSetiausaha": "<PERSON><PERSON>", "frequencyOrdinaryGeneralMeetings": "Kekerapan mesyuarat agung biasa", "numberOfAuditors": "Bilangan juru audit", "realEstateAdministration": "Pentadbiran harta tak alih", "deadlineForOrdinaryGeneralMeeting": "<PERSON><PERSON><PERSON> akhir untuk mesyuarat agung biasa", "feedbackFromSupremeCommittee": "<PERSON><PERSON><PERSON> Balas AJK Tertinggi", "lastDateOrdinaryGeneralMeeting": "<PERSON><PERSON><PERSON> a<PERSON> agun<PERSON>a", "docList": "<PERSON><PERSON><PERSON>", "statusDokumen": "Status Dokumen", "choose": "<PERSON><PERSON><PERSON>", "noRecordForStatus": "<PERSON><PERSON><PERSON>an tiada dalam rekod. Sila hubungi JPPM Negeri untuk menyemak status pertubuhan anda.", "clauseContentOri": "<PERSON><PERSON><PERSON><PERSON>", "alirantugasPopText": "<PERSON><PERSON><PERSON> anda pasti untuk aktif alir tug<PERSON> Pengurusan AJK & Keahlian daripada Jawatankuasa ini?", "pindaanRemindText": "<PERSON>la isi maklumat mesyuarat pindaan per<PERSON>aan", "here": "disini", "pindaanRemindText1": "bagi kemasukan maklumat mesyuarat terlibat.", "PurposeOfConstitutionalAmendment": "<PERSON><PERSON><PERSON> pin<PERSON>an <PERSON>", "decisionOfAppealApplication": "Keputusan <PERSON>", "kelulusanRayuanTabTitle1": "Pembatalan pendaftaran pertubuhan di bawah seksyen 2A", "kelulusanRayuanTabTitle2": "Keengganan mendaftarkan pertubuhan itu di bawah seksyen 7", "kelulusanRayuanTabTitle3": "Keengganan memberi pengecualian di bawah seksyen 9A (4)", "kelulusanRayuanTabTitle4": "Keengganan meluluskan pindaan kepada kaedah-ka<PERSON>hnya di bawah seksyen 11", "kelulusanRayuanTabTitle5": "Keengganan meluluskan penubuhan suatu cawangan pertubuhan di bawah seksyen 12", "kelulusanRayuanTabTitle6": "<PERSON>intah melarangkan orang-orang bukan warganegara seksyen 13A (1)", "listOfApealApplications": "<PERSON><PERSON><PERSON>", "applicant": "<PERSON><PERSON><PERSON><PERSON>", "orgAppealApplicationInfo": "Maklumat permohonan rayuan per<PERSON>uhan", "detailsForAppeal": "Butiran/ alasan permohonan rayuan", "showConstitution": "<PERSON><PERSON>", "showWhiteningHistory": "Papar $t(historyOfWhitening)", "selectStatementYear": "<PERSON><PERSON><PERSON> tahun penyata", "queryTo": "<PERSON><PERSON>", "letterNo": "No. Surat", "rekodDijumpai": "<PERSON><PERSON><PERSON>", "senaraiPertubuhan": "<PERSON><PERSON><PERSON>", "noPPM": "No. PPM", "statusMigrasi": "Status Migrasi", "namaPersatuan": "<PERSON><PERSON> persatuan", "tarikhDidaftar": "<PERSON><PERSON><PERSON> didaftar", "tarikhBatal": "<PERSON><PERSON><PERSON>", "jikaAda": "<PERSON><PERSON> ada", "tarikhLulusPindaan": "<PERSON><PERSON><PERSON> lulus pindaan", "paparSenaraiPindaan": "<PERSON><PERSON> <PERSON><PERSON>", "paparSejarahPembatalan": "<PERSON><PERSON>", "notaPengarah": "<PERSON>a pen<PERSON>", "tarikhAlihPermohonan": "<PERSON><PERSON><PERSON> alih permohonan", "maklumatPembaharuanSetiausaha": "Maklumat Pembaharuan <PERSON>", "migrasi": "<PERSON><PERSON><PERSON>", "kekerapanMesyuaratAgungBiasa": "Kekerapan Mesyuarat Agung Biasa", "tarikhAkhirMesyuaratAgungBiasa": "<PERSON><PERSON><PERSON> untuk Mesyuarat Agung Biasa", "bilanganJuruaudit": "Bilangan juru<PERSON>it", "pentadbiranHartaTakAlih": "Pentadbiran Harta Tak Alih", "petaLokasiTempatUrusan": "Peta lokasi tempat urusan", "nomborFaks": "Nombor faks", "maklumatKebajikan": "Maklumat Kebajikan", "rekodBukanWarganegaraDijumpai": "Rekod bukan warganegara dijumpai", "tarikhCipta": "<PERSON><PERSON><PERSON> cipta", "maklumatBukanWarganegara": "Maklumat bukan warganegara", "pegawaiYangMeluluskan": "Peg<PERSON><PERSON> yang melu<PERSON>kan", "noHPRumah": "No. HP/Rumah", "emelSetiausaha": "<PERSON><PERSON>", "sijilMigrasiCawanganPenukarSUCawangan": "Si<PERSON>l <PERSON>n/ Penukaran SU Cawangan", "senaraiPindaanNamadanAlamatCawangan": "<PERSON><PERSON><PERSON> dan <PERSON><PERSON>", "pemegangJawatan": "<PERSON><PERSON>egang jawatan", "ahliBukanWarganegara": "<PERSON><PERSON> bukan wargan<PERSON>ara", "senaraiCawangan": "<PERSON><PERSON><PERSON>", "nomborCawangan": "No Cawangan", "pegawaiBertanggungjawab": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "catatanJustifikasiKeputusan": "Catatan/Justifikasi Keputusan", "maklamatMesyuarat": "Matlamat <PERSON>ua<PERSON>", "paparMinitMesyuarat": "<PERSON><PERSON> minit <PERSON>", "senaraiPenukaranICSUCawanganMigrasi": "Senarai Penukaran IC SU Cawangan Migrasi", "senaraiAJKCawangan": "Senarai AJK Cawangan", "namaPemegangAwam": "<PERSON><PERSON> pem<PERSON>g awam", "maklumatNamaPegawai": "Mak<PERSON>at nama pegawai", "noTelefonHPRumahPejabat": "No. telefon (HP/ Rumah/ Pejabat)", "pegawaiHarta": "Pegawai harta", "negeriCawangan": "<PERSON><PERSON><PERSON>", "kemaskiniICSUCawangan": "Kemaskini IC SU Cawangan", "noPPMLama": "No PPM lama", "namaSUSemasa": "Nama SU semasa", "noKPSemasa": "No KP Semasa", "icBaru": "IC baru", "namaBaru": "<PERSON>a baru", "sijilMigrasiCawanganPeringatan": " Halaman ini memaparkan senarai cawangan MIGRASI yang telah mempunyai Setiausaha. Klik pada ikon “pensil” untuk kemaskini nama dan no pengenalan SU Cawangan.", "maklumatCawanganSemasa": "<PERSON><PERSON><PERSON><PERSON> cawangan semasa", "tarikhDibenarkan": "<PERSON><PERSON><PERSON>", "tarikhDitubuhkan": "<PERSON><PERSON><PERSON>", "statusCawanganSemasa": "Status cawangan semasa", "kemaskiniNamaCawangan": "<PERSON><PERSON><PERSON> nama cawangan", "noPPMLamaCawangan": "No PPM lama cawangan", "namaCawanganSemasa": "<PERSON><PERSON> ca<PERSON>an se<PERSON>a", "namaCawanganBaru": "<PERSON>a cawangan baru", "kemaskiniStatusCawanganSemasa": "Kemaskini status cawangan semasa", "statusSemasa": "Status semasa", "statusBaru": "Status baru", "pertubuhanKemaskiniPeringatan": " <PERSON><PERSON> boleh men<PERSON> maklum<PERSON>.", "maklumatPertubuhanSemasa": "Ma<PERSON><PERSON><PERSON> pertubuhan semasa", "statusPertubuhanSemasa": "Status pertubuhan semasa", "kategoriSemasa": "<PERSON><PERSON><PERSON>", "subKategoriSemasa": "Sub-kate<PERSON>i semasa", "tarikhDaftar": "<PERSON><PERSON><PERSON>", "kemaskiniNamaPertubuhan": "<PERSON><PERSON><PERSON> nama per<PERSON>an", "namaAsalPertubuhan": "<PERSON><PERSON>", "namaSemasa": "<PERSON><PERSON> se<PERSON>a", "kemaskiniAlamatPertubuhan": "<PERSON><PERSON><PERSON> al<PERSON><PERSON>", "kemaskiniAlamatSuratMenyuratPertubuhan": "Ke<PERSON><PERSON> alamat surat menyurat per<PERSON>an", "kemaskiniStatusPertubuhan": "Kemaskini status pertubuhan", "kemaskiniKategoriPertubuhan": "<PERSON><PERSON><PERSON> kate<PERSON>i <PERSON>", "kategoriBaru": "<PERSON><PERSON><PERSON> baru", "subKategoriBaru": "Sub kategori baru", "kemaskiniTarafPertubuhan": "<PERSON><PERSON><PERSON> ta<PERSON><PERSON>", "tarafSemasa": "<PERSON><PERSON>", "tarafBaru": "<PERSON><PERSON> baru", "annualStatementRecord": "<PERSON><PERSON><PERSON>", "diluluskanOleh": "Diluluskan oleh", "maklumatPermohonanPembubaranPertubuhan": "Maklumat <PERSON>", "sebabRayuan": "<PERSON><PERSON><PERSON>", "noPengenalanSetiausaha": "No. Pengenalan Setiausaha", "pppBertanggungjawab": "PPP Bertanggungjawab/syor", "kppBertanggungjawab": "KPP Bertanggungjawab/syor", "pendaftarSyor": "Pendaftar/syor", "statusTahunPenyata": "Status Penyata Tahun", "maklumatTahunPenyata": "Mak<PERSON>at tahun penyata", "tahunPenyataSemasa": "<PERSON><PERSON> penyata semasa", "paparPeyataTahunan": "<PERSON><PERSON>", "noPendaftaranLama": "No pendaftaran lama", "senaraiKelabu": "<PERSON><PERSON><PERSON> k<PERSON>", "senaraiHitamKeputihan": "<PERSON><PERSON><PERSON> hitam / keputihan", "semakanIndividu": "Semakan individu", "semakanNoPendaftaranLama": "Semakan no pendaftaran lama", "noPPP": "No PPP", "semakanNoPengenalanDiriPemegangJawatan": "Semakan No. Pengenalan Diri Pemegang Jawatan", "maklumatPegawaiPenyemak": "Maklumat pegawai penyemak", "noPengenalanDiriPenyemak": "No. Pengenalan Diri penyemak", "namaPegawaiPenyemak": "Name pegawai <PERSON>k", "tarikhDanMasaSemak": "<PERSON><PERSON><PERSON> dan masa semak", "namaIndividu": "<PERSON><PERSON> individu", "noPengenalanBaru": "No pengenalan baru", "noPengenalanLama": "No pengenalan lama", "tarikhMati": "<PERSON><PERSON><PERSON> mati", "jikaBerkanaan": "<PERSON><PERSON>", "tarafPenduduk": "<PERSON><PERSON>", "alamatTetap": "<PERSON><PERSON><PERSON> tetap", "bilanganPertubuhanTerkini": "Bilangan per<PERSON> te<PERSON>i", "pertubuhanYangDianggotai": "<PERSON><PERSON><PERSON><PERSON> yang <PERSON>", "statusPenama": "Status penama", "bilanganCawanganTerkini": "Bilangan cawangan terkini", "cawanganYangDianggotai": "Cawangan yang di<PERSON>ai", "additionSecretaryRenewal": "<PERSON><PERSON><PERSON><PERSON>", "applicationName": "<PERSON><PERSON>", "ROAction": "Tindakan RO", "JPPMComments": "<PERSON><PERSON><PERSON>", "loading": "Memuatkan", "branchRegistrationSteps": "<PERSON><PERSON><PERSON>", "companyName": "<PERSON><PERSON>", "organizationMeeting": "<PERSON><PERSON><PERSON><PERSON>", "enableConfirmation": "<PERSON><PERSON>h anda pasti untuk mengaktifkan fungsi ini?", "disableConfirmation": "<PERSON><PERSON>h anda pasti untuk nyahaktifkan fungsi ini?", "applicationSuccessfulySubmited": "<PERSON><PERSON><PERSON><PERSON><PERSON> anda ber<PERSON>a <PERSON>", "actualPosition": "Jawatan hakiki", "acceptMemberApplication": "<PERSON><PERSON>h anda pasti untuk menerima pemohonan ahli ini?", "rejectMemberApplication": "<PERSON><PERSON>h anda pasti untuk menolak pemohonan ahli ini?", "membershipApplicationSuccessful": "<PERSON><PERSON><PERSON><PERSON><PERSON> penolakan ahli telah berjaya", "inCountry": "<PERSON><PERSON> negara", "abroad": "<PERSON><PERSON> negara", "notWorking": "Tidak Bekerja", "working": "<PERSON><PERSON><PERSON>", "associationAwaitingApproval": "Per<PERSON>uan <PERSON> kel<PERSON>an <PERSON>", "branchNameHelper": "<PERSON><PERSON> henda<PERSON> bermula den<PERSON>,<PERSON><PERSON><PERSON><PERSON>.", "memberAttendance": "<PERSON><PERSON><PERSON><PERSON>", "organisationAppeal": "<PERSON><PERSON>", "meetingAttendanceNotice": "Pengguna digalakkan untuk mengisi maklumat kehadiran Ahli Jawatankuasa", "minAjkMeeting": "Bilangan orang ahli jawatankuasa untuk panggilan mesyuarat", "mohon": "<PERSON><PERSON>", "checkFeedbacks": "Semak cadangan dan mak<PERSON>", "invalidName": "Ralat : <PERSON><PERSON><PERSON> dan <PERSON>a tidak sepadan seperti di MyKad.", "phoneNumberLessThan7": "Ralat : Nombor telefon mesti sekurang-kurangnya 7 digit.", "Thepurposeofthemeetingisto": "<PERSON><PERSON><PERSON> me<PERSON><PERSON> ad<PERSON>h untuk…", "maklumatMesyuarat2": "Maklumat mesyuarat", "totalAttendMember": "<PERSON><PERSON><PERSON>", "pegawaiAwam": "Pegawai <PERSON>", "passwordRequirements2": "Masukkan sekurang-kurangnya 8 aksara dengan gabungan huruf besar, huruf kecil dan aksara khas.", "meetingLocation": "<PERSON><PERSON>", "alamatTempatUrusan1": "<PERSON><PERSON><PERSON> Urusan", "registerNewUser": "Daftar Pengguna Baharu", "byContinuingYouAgreeTo": "<PERSON><PERSON>, anda bersetuju dengan", "myKadNo": "No. MyKad", "myKadPlaceholder": "No. MyKad/MyPR/Passport", "OTPDialogConfTitle": "Kod OTP telah dihantar ke e-mel anda", "OTPDialogConfContent": "<PERSON>la semak e-mel yang didaftarkan dan set kata laluan baharu", "OTPVerification": "Pengesahan OTP", "resetSuccessful": "<PERSON><PERSON> la<PERSON>an berjaya diset semula", "phoneNoVerification": "Pengesahan Nombor Telefon", "passwordSettingSuccessful": "<PERSON><PERSON><PERSON> kata laluan berjaya.", "emailOTPFailed": "Kod yang dimasukkan adalah salah, sila cuba lagi", "emailVerified": "E-mel anda telah disahkan.", "nonCitizenIdNo": "Nombor ID Pengguna", "timRangeAlertMsg": "Masa mula tidak boleh melebihi masa tamat.", "emailVerificationSuccessful": "Pengesahan OTP E-mel Berjaya", "phoneVerificationSuccessful": "Pengesahan OTP Nombor Telefon Berjaya", "emailVerificationFailed": "Pengesahan OTP E-mel gagal", "phoneVerificationFailed": "Pengesahan OTP Nombor Telefon gagal", "veryWeak": "Sangat Lemah", "weak": "Lemah", "fair": "<PERSON><PERSON><PERSON>", "good": "Baik", "strong": "<PERSON><PERSON><PERSON>", "applicationSuccessDeleted": "<PERSON><PERSON><PERSON><PERSON> anda berjaya di padam", "applicationSuccessSubmited": "<PERSON><PERSON><PERSON><PERSON> anda berjaya di hantar", "confirmDeleteApplication": "<PERSON><PERSON>h anda pasti untuk padam pemohonan ini?", "confirmDeleteBranch": "<PERSON><PERSON><PERSON> anda pasti untuk menghapus cawangan \"{{name}}\"?", "participation": "<PERSON><PERSON><PERSON>", "association": "Persatuan", "seeFullView": "<PERSON><PERSON>", "chatBox": "ChatBox", "chatWithKakRos": "<PERSON><PERSON> be<PERSON><PERSON>", "registerNameToolTip": "<PERSON>la masukkan nama penuh seperti di MyKad", "registerNameToolTip2": "<PERSON><PERSON> masukkan nama penuh anda", "tarafPenubuhan": "<PERSON><PERSON>", "hari": "hari", "mesyuaratPenubuhan": "<PERSON><PERSON><PERSON><PERSON>", "newBranchSecretaryInformation": "Maklumat <PERSON>", "branchNameHelper1": "Cadangan: <PERSON><PERSON> boleh bermula dengan <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "pleaseSelectRole": "<PERSON>la pilih peranan anda", "pleaseSelectNationality": "<PERSON>la pilih warganegara anda untuk meneruskan pendaftaran", "phoneNumberIncorrectFormat": "Ralat : Nombor Telefon yang dimasukkan tidak mengikut format.Sila masukkan semula mengikut format yang disediakan.", "emailNumberIncorrectFormat": "Ralat : <PERSON><PERSON> yang dimasukkan tidak mengikut format. Sila masukkan semula mengikut format yang disediakan.", "exampleEmail": "<EMAIL>", "userExistsError": "Ralat : <PERSON><PERSON><PERSON> ini sudah berdaftar di dalam sistem.", "phoneExistsError": "Ralat : Nombor telefon telah wujud dalam sistem. <PERSON>la gunakan nombor lain.", "emailExistsError": "Ralat : <PERSON>-mel ini telah didaft<PERSON>an. <PERSON><PERSON> gunakan emel lain.", "integrationOffError": "Mohon maaf. Pendaftaran pengguna tidak dapat dilakukan buat sementara waktu. Sila cuba lagi dalam tempoh 4 jam. <PERSON><PERSON> kasih.", "terima": "Terima", "membershipAccpetApplicationSuccessful": "<PERSON><PERSON><PERSON><PERSON><PERSON> kema<PERSON>kan ahli telah berjaya.", "rulePassword3": "Masukkan sekurang-kurangnya 8 aksara dengan gabungan huruf besar, huruf kecil dan aksara khas.", "rowsPerPage": "<PERSON><PERSON> set<PERSON> ha<PERSON>an:", "rowsPerPageListCategoryJPM": "<PERSON><PERSON> rekod set<PERSON> ha<PERSON>an:", "dateAndBirthPlace": "Tarikh & tempat lahir", "payOnlineRenewIn24": "<PERSON><PERSON><PERSON> tahunan perlu dihantar dalam tempoh 60 hari selepas Mesyuarat Agung atau dalam tempoh 60 hari selepas berakhir tahun kalendar jika tiada mesyuarat agung dijalankan pada tahun tersebut.", "pleaseReferState": "Sila rujuk negeri berdekatan jika bayaran tidak berubah.", "lanjutMasaWarning1": "<PERSON><PERSON><PERSON> medan bertanda", "lanjutMasaWarning2": "mesti diisi. Lanjutan masa dibenarkan sekali sahaja.", "renewalDate": "<PERSON><PERSON><PERSON>", "newBranchSecretary": "<PERSON><PERSON><PERSON><PERSON>", "oldBranchSecretary": "<PERSON><PERSON><PERSON><PERSON>", "validation": {"required": "Medan ini diperlukan", "meetingTimeToInvalid": "Masa mula tidak boleh melebihi masa tamat.", "mustBeNumber": "Medan ini mesti berupa nombor.", "minValue": "<PERSON><PERSON> mesti sekurang-kurangnya {{value}}.", "invalidTimeDuration": "<PERSON><PERSON><PERSON> masa tidak valid", "documentSize": "Saiz fail me<PERSON><PERSON><PERSON> had {{maxSize}}MB. Sila muat naik fail yang lebih kecil", "invalidFileType": "Jenis fail tidak sah. <PERSON>la muat naik fail yang disokong."}, "pindaanApproverTitle": "<PERSON><PERSON>uan menunggu kelulusan permohonan pindaan perlembagaan", "minute": "<PERSON><PERSON>", "permohonan": "<PERSON><PERSON><PERSON><PERSON>", "pengerusiCawangan": "Pen<PERSON><PERSON><PERSON>", "timbalanPresiden": "<PERSON><PERSON><PERSON>n", "setiausahaAgung": "<PERSON><PERSON><PERSON><PERSON>", "setiausahaCawangan": "<PERSON><PERSON><PERSON><PERSON>", "penolongSetiausahaAgung": "Penolong <PERSON>", "bendahariAgung": "<PERSON><PERSON><PERSON>", "ketuaBendahari": "<PERSON><PERSON><PERSON>", "bendahariKehormat": "<PERSON><PERSON><PERSON>", "penolongBendahariAgung": "Penolong Bendahari Agung", "penolongKetuaBendahari": "Penolong <PERSON>", "penolongBendahariKehormat": "Penolong <PERSON>", "ahliJawatanBiasa": "<PERSON><PERSON>", "lainLain": "Lain-lain", "biroEkonomi": "<PERSON><PERSON>", "biroKebajikan": "<PERSON><PERSON>", "biroSukanDanSosial": "B<PERSON> Sukan dan <PERSON>", "biroAgama": "<PERSON><PERSON>", "pindaanNameAlamatBranch": "<PERSON><PERSON><PERSON>", "pinda": "Pinda", "choosePindaan": "<PERSON><PERSON><PERSON> dan <PERSON>", "pinda_nama_alamat": "<PERSON>nda nama dan alamat", "pinda_nama": "<PERSON>nda nama", "pinda_alamat": "<PERSON><PERSON> al<PERSON>", "branchnameChangeInfo": "Branch name change information", "branchnameaddresschange": "Branch name and address change information", "areYouSureOfTheApplicationOutcome": "<PERSON><PERSON><PERSON>h anda pasti dengan keputusan permohonan ini?", "meetingPindaanTitle": "Maklumat Mesyuarat melulus<PERSON> pindaan", "reminderPindaanTitle": "Jika Senarai Mesyuarat tiada dalam pilihan, klik disini bagi kemasukan maklumat mesyuarat pindaan nama dan alamat cawangan.", "selectAmendBranch": "<PERSON><PERSON><PERSON>", "catatan300": "Catatan (had 300 perkataan)", "masaUlusan": "<PERSON><PERSON>", "persatuanMenungguUlasanLuar": "Persatuan menunggu kelulusan Induk menunggu ulasan luar", "senaraiMenungguUlasanLuar": "<PERSON><PERSON><PERSON> menunggu kel<PERSON>an induk - menunggu ulasan luar", "sejarahUlasanLuar": "<PERSON><PERSON><PERSON>i luar", "OrganizationNo": "No <PERSON><PERSON>uhan", "resultdateif": "<PERSON><PERSON><PERSON> (Jika ada)", "clear": "Kosongkan", "roleNameAlreadyInUse": "Ralat: <PERSON><PERSON><PERSON> ini telah wujud.", "noAvailableDocuments": "Tiada Dokumen Tersedia", "ViewMeetings": "<PERSON><PERSON>", "updateConstitution": "Kemaskini Perlembagaan", "comfirmUpdatePindaan": "<PERSON><PERSON><PERSON> anda pasti ingin mengemaskini jenis perlembagaan pertubuhan ini? <PERSON><PERSON> ya, maklumat yang telah diisi sebelum ini akan dipadam.", "individualAppeal": "<PERSON><PERSON> individu", "rayuanQA1": "<PERSON><PERSON>hon<PERSON> rayuan kepada Menteri boleh dibuat dalam tempoh masa tiga puluh hari dari tarikh keputusan.", "rayuanQA2": "Pemohon dikehendaki untuk mengemukakan dokumen-dokumen sokongan dan bayaran permohonan sebanyak RM50.00.", "BUBAR_SEMENTARA": "B<PERSON>r Se<PERSON>", "BARU": "<PERSON><PERSON>", "TIDAK_AKTIF": "Tidak Aktif", "DILUPUSKAN": "Dilupuskan", "DIGUGURKAN": "Digu<PERSON><PERSON><PERSON>", "TIADA_MAKLUMAT": "Tiada Ma<PERSON>lumat", "TEST": "<PERSON><PERSON><PERSON>", "PINDA_NAMA": "<PERSON><PERSON>", "PERTELINGKAHAN_SIASATAN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TIDAK_AKTIF_PINDAH": "Tidak Aktif <PERSON>", "DALAM_PERHATIAN": "<PERSON><PERSON>", "TIDAK_AKTIF1": "Tidak Aktif 1", "PADAM": "Padam", "DILUPUTKAN": "Diluputkan", "CancellationRegistration2A": "Pembatalan pendaftaran pertubuhan di bawah Seksyen 2A.", "RejectionApplication7": "Penolakan permohonan pendaftaran di bawah Seksyen 7.", "RejectionExemption9A1a_9A1b_9A1c": "Penolakan permohonan pengecualian Seksyen 9A(1)(a), 9A(1)(b) dan 9A(1)(c).", "RejectionApplication11": "Penolakan permohonan pindaan <PERSON>-undang Seksyen 11.", "RejectionApplicationBranch12": "Penolakan permohonan pen<PERSON>uhan cawangan bawah Se<PERSON>yen 12.", "CancellationOrganisation13": "Pembatalan pendaftaran pertubuhan di bawah Seksyen 13.", "Order_13A1": "Perintah 13A(1).", "Order_13A2": "Perintah 13A(2).", "RejectionApplicationAppointment144": "Penolakan permohonan per<PERSON>n juru<PERSON><PERSON> b<PERSON><PERSON> 14(4).", "Order_145": "<PERSON>intah 14(5).", "CancellationOrganisation16": "Pembatalan pendaftaran pertubuhan di bawah Seksyen 16.", "RejectionApplicationOffice49": "Penolakan permohonan memegang jawatan bagi mereka yang disenarai-hitamkan di bawah Seksyen 49.", "mykad/lesen": "<PERSON><PERSON><PERSON>/<PERSON><PERSON>", "emelSyarikat": "<PERSON><PERSON>", "senaraiPermintaan": "<PERSON><PERSON><PERSON>", "successful": "<PERSON><PERSON><PERSON><PERSON>", "ascending": "Menaik", "descending": "<PERSON><PERSON><PERSON>", "KandunganPenuh": "<PERSON><PERSON><PERSON><PERSON> penuh", "senaraiDataDiperlukan": "Senarai data diperlukan", "applicationStatusCode": "Status Permohonan", "paymentYear": "<PERSON><PERSON><PERSON>", "pleaseUpdateFasalText": "<PERSON>la kemas kini kandungan fasal anda di sini.", "appealWaitingList": "<PERSON><PERSON><PERSON>", "rayuanKaunterAlert": "Sila jelaskan pembayaran di kaunter dalam tempoh 30 hari dari tarikh permohonan.", "errorUserExistUserCreation": "Pengguna dengan maklumat ini sudah berdaftar.", "markAsRead": "Tanda Sebagai Telah Dibaca", "noMoreNotification": "Tiada notifikasi lagi", "noMoreNotification2": "<PERSON><PERSON> akan memak<PERSON>kan anda apabila terdapat sesuatu yang baharu untuk anda.", "seeAllNotifications": "<PERSON><PERSON> k<PERSON> notif<PERSON>", "hariIni": "<PERSON>", "semalam": "Semalam", "7HariTerakhir": "7 <PERSON>", "sebulanTerakhir": "<PERSON><PERSON><PERSON>", "sebelumnya": "Sebelumnya", "changeBranchName": "Maklumat Pinda Nama Cawangan", "changeBranchAddress": "Maklumat Alama<PERSON>", "confirmdeletefasalText": "<PERSON><PERSON>h anda pasti untuk padam fasal ini?", "deleteAhliConfirmation": "<PERSON><PERSON>h anda pasti untuk memadam rekod ahli ini?", "non-citizen": "<PERSON><PERSON><PERSON>", "reminderPindaanTitle1": "Jika Senarai <PERSON> tiada da<PERSON> p<PERSON>han, klik", "reminderPindaanTitle2": "bagi kemasukan maklumat mesyuarat pindaan nama dan alamat cawangan.", "clickhere": "disini ", "IcDoesNotExist": "Nombor pengenalan ini tidak wujud", "nonCitizenDecisionWaitingList": "<PERSON><PERSON><PERSON>gu keputusan bukan warganegara", "applicationAwaitingDecision": "<PERSON><PERSON><PERSON><PERSON> kep<PERSON>n", "stepAmendmentBranchNameAndAddress": "<PERSON><PERSON><PERSON>", "fillMeeting": "<PERSON><PERSON>", "selectBranchAmend": "<PERSON><PERSON><PERSON>", "bacaanBerkaitan": "Bacaan Berkaitan", "penyampaianPerkhidmatanFooter": "<PERSON><PERSON><PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON>", "error": "<PERSON><PERSON>", "sureToSubmitFeedback": "<PERSON><PERSON>h anda pasti untuk hantar maklumbalas ini?", "personalIdNo": "Nombor Pengenalan Diri", "usereROSESv2": "Verifikasi pengguna bagi Sistem eROSES versi 2.0", "AddClause": "Tambah Fasal", "confirmBuyDokumen": "<PERSON><PERSON>h anda pasti untuk membeli dokumen ini?", "geran": "Geran", "Pengumuman": "<PERSON><PERSON><PERSON>", "contohJan": "Contoh: 1 Jan<PERSON>ri", "KebangsaanPopText": "Kebangsaan: <PERSON><PERSON><PERSON> henda<PERSON>h sekurang-kurangnya dari 7 negeri berbeza.", "negeriPopText": "Negeri: <PERSON><PERSON><PERSON> henda<PERSON>h dari negeri yang sama.", "lainlainPopText": "Lain-lain: <PERSON><PERSON> oleh ahli dari mana-mana negeri. ", "pleaseEnterName": "<PERSON><PERSON> masukkan nama", "pleaseSelectCategory": "<PERSON>la pilih kategori", "pleaseSelectState": "<PERSON>la pilih negeri", "pleaseSelectStatus": "Sila pilih status", "recordSuccesDeleted": "<PERSON><PERSON><PERSON> anda berjaya dipadam", "branchNameDetails1": "<PERSON><PERSON>", "confirmSubmitStatementText": "<PERSON><PERSON>h anda pasti untuk hantar penyata tahunan ini?", "confessionRecorded": "<PERSON><PERSON><PERSON><PERSON>", "NEW": "<PERSON><PERSON>", "DALAM_TINDAKAN": "<PERSON><PERSON>", "COMPLETE": "Se<PERSON><PERSON>", "PENDING_IT": "Tenggal IT", "CLOSE": "<PERSON><PERSON><PERSON>", "REOPEN": "<PERSON><PERSON> semula", "AJKNoUpdateConfirmation": "<PERSON><PERSON><PERSON> anda pasti untuk mengemaskini bilangan ahli jawatank<PERSON>sa? Tindakan ini akan mempengaruhi bahagian Senarai AJK", "seorang": "se<PERSON>g", "tukarSetiausaha": "<PERSON><PERSON>", "exampleYearPlaceholder": "Contoh: 1 tahun", "messageKeputusanPermohonanSuccessPindaanName": "Keputusan permohonan pindaan nama dan alamat cawangan berjaya dihantar", "messageKeputusanPermohonanErrorPindaanName": "Keputusan permohonan pindaan nama dan alamat cawangan gagal dihantar", "messageKeputusanPermohonanSuccessLanjutmasa": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>", "messageKeputusanPermohonanErrorLanjutmasa": "<PERSON><PERSON><PERSON><PERSON>", "stateOfBirth": "<PERSON><PERSON><PERSON>", "max2000": "Maksimum: RM2,000", "personalIdNo2": "No Pengenalan Diri", "memberRegistrationDate": "<PERSON><PERSON><PERSON>", "memberType": "<PERSON><PERSON>", "noNBIDCawangan": "No NBID Cawangan", "VIEW_ALL": "<PERSON><PERSON>", "ahliBersekutu": "<PERSON><PERSON>", "ahliKehormat": "<PERSON><PERSON>", "seumurHidup": "<PERSON><PERSON><PERSON>", "ahliRemaja": "<PERSON><PERSON>", "alamatSuratMenyuratCawangan": "<PERSON><PERSON><PERSON> Menyurat Cawangan", "alamatTempatUrusanCawangan": "Alamat Tempat Urusan Cawangan", "peringatanCapFirst": "Peringatan", "pertubuhanAnda": "<PERSON><PERSON><PERSON><PERSON>", "organizationManagementSystem": "<PERSON><PERSON><PERSON>", "amendmentsConstitutionAlert": "Se<PERSON>ng Pindaan Perlembagaan perlu dikemukakan kepada pendaftar melalui sistem eROSES dalam masa 60 hari selepas mesyuarat yang memutuskan pindaan perlembagaan tersebut.", "moreThan7ajk": "Bilangan Ahli Jawatankuasa mestilah lebih daripada 7", "nomorethan31": "Tidak boleh mele<PERSON>hi da<PERSON>ada 31", "ordinaryCommitteeMember1": "<PERSON><PERSON>", "thepresident": "<PERSON>", "thepresidentBranch": "Yang Dipertua Cawangan", "Exceededtheperiod": "<PERSON><PERSON><PERSON><PERSON> <PERSON>h", "pegawaiAwamMainTitle": "<PERSON><PERSON><PERSON><PERSON> pegawai awam (menunggu kelulusan)", "pengesahanPegawaiHarta": "Pengesahan pegawai harta", "confirmDeleteAudit": "<PERSON><PERSON><PERSON> anda pasti untuk memadam juruaudit ini", "return": "Kembali", "auditorDeletedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>adam", "auditorAddConfirmation": "<PERSON><PERSON><PERSON> anda pasti untuk menambah juruaudit ini ?", "auditorCreatedSuccessfully": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON> di<PERSON>", "timbalanyYangDipertua": "<PERSON><PERSON><PERSON>", "naibYangDipertua": "<PERSON><PERSON>", "fasalCawangan": "Cawangan", "branchName": "<PERSON><PERSON>", "pengurusanPerlembangaanPayment": "<PERSON><PERSON><PERSON><PERSON> pindaan undang-undang telah dire<PERSON>kan", "appealPayment": "<PERSON><PERSON><PERSON><PERSON> rayuan telah <PERSON>", "newBranchName": "<PERSON><PERSON>", "newBranchBusinessAddress": "Alamat Baru Tempat Urusan Cawangan", "newBranchCorrespondenceAddress": "<PERSON><PERSON><PERSON> Surat Menyurat Cawangan", "BranchCorrespondenceAddress": "<PERSON><PERSON><PERSON> Menyurat Cawangan", "jobInformation": "Mak<PERSON><PERSON>", "workAddress": "Alamat Tempat Bekerja", "noTelefon": "No. telefon", "noSyarikat": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "companyAddress": "<PERSON><PERSON><PERSON>", "memberApprovalDate": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>)", "membershipNo": "<PERSON><PERSON>", "noTelephone": "No. Telefon", "DivisionStateJPPMBranch": "Bahagian / Negeri / Cawangan JPPM", "noncitizenRegList": "<PERSON><PERSON><PERSON> kel<PERSON>an bukan warganegara", "pegawaiAwamRegList": "<PERSON><PERSON><PERSON> kel<PERSON>an pegawai awam", "pegawaiHartaRegList": "<PERSON><PERSON><PERSON> kel<PERSON>an pegawai harta", "createMeetingReminder": "Sila isi mesyuarat di Pengurusan Mesyuarat terlebih dahulu.", "penambahanKalendar": "<PERSON><PERSON><PERSON><PERSON>", "identityCardNo": "No. <PERSON><PERSON>", "Viewlist": "<PERSON><PERSON>", "confirmDeleteContribution": "<PERSON><PERSON><PERSON> anda pasti untuk padam sumbangan ini", "meetingInfoConfirmation": "<PERSON>lah dengan ini saya mengesahkan bahawa segala maklumat yang diberikan adalah benar. Jika pihak Jabatan Pendaftaran Pertubuhan Malaysia mendapati berlaku penipuan dan kepalsuan dalam keterangan dokumen yang telah saya berikan di atas, maka pihak Jabatan Pendaftaran Pertubuhan Malaysia adalah dengan ini berhak untuk menolak permohonan saya dan jika disabitkan bersalah, saya boleh dikenakan denda tidak melebihi denda RM 2000 mengikut seksyen 54 A, AKTA PERTUBUHAN 1966", "roDirectorNote": "Catatan RO/Pengarah", "rayuanQA3": "<PERSON><PERSON> rayuan perlu dijawab dalam tempoh masa tujuh hari dari tarikh kuiri diterima.", "confirmDeletePenyata": "<PERSON><PERSON>h anda pasti untuk padam penyata tahunan ini?", "furnitofficeEquipmentSuppliesure": "<PERSON><PERSON><PERSON>", "NumberofPositions": "Bilangan Jawatan", "JobTitle": "Gelaran Jawatan", "roleName": "<PERSON><PERSON>", "completedAuditedFinancialStatement": "<PERSON><PERSON><PERSON> yang telah selesai diaudit", "uploadFinancialStatements": "<PERSON><PERSON>", "DownloadFinancialStatementTemplate": "<PERSON><PERSON>", "confirmDeactivateTaskFlowPenyataTahunan": "<PERSON><PERSON><PERSON> anda pasti untuk nyah aktif alir tugas Penyata Tahun<PERSON> da<PERSON>ada <PERSON>tankuasa ini?", "confirmactivateTaskFlowPenyataTahunan": "<PERSON><PERSON><PERSON> anda pasti untuk aktif alir tugas Penyata Ta<PERSON> da<PERSON>ada <PERSON> ini?", "confirmDeactivateTaskFlowMesyuarat": "<PERSON><PERSON><PERSON> anda pasti untuk nyah aktif alir tug<PERSON> Pengurusan Mesyuarat daripada Jawatankuasa ini?", "confirmactivateTaskFlowMesyurat": "<PERSON><PERSON><PERSON> anda pasti untuk aktif alir tug<PERSON>gu<PERSON>an Mesyuarat daripada Jawatankuasa ini?", "confirmDeactivateTaskFlowPerlembagaan": "<PERSON><PERSON><PERSON> anda pasti untuk nyah aktif alir tugas Pengurus<PERSON> Perlembagaan daripada Jawatankuasa ini?", "confirmactivateTaskFlowPerlembagaan": "<PERSON><PERSON><PERSON> anda pasti untuk aktif alir tug<PERSON> Pengu<PERSON> Perlembagaan daripada Jawatankuasa ini?", "confirmDeactivateTaskFlowAJK": "<PERSON><PERSON><PERSON> anda pasti untuk nyah aktif alir tug<PERSON> Pengurusan AJK & Keahlian daripada Jawatankuasa ini?", "confirmactivateTaskFlowAJK": "<PERSON><PERSON><PERSON> anda pasti untuk aktif alir tug<PERSON> Pengurusan AJK & Keahlian daripada Jawatankuasa ini?", "ApplicationApproved": "<PERSON><PERSON><PERSON><PERSON>", "ApplicationRejected": "<PERSON><PERSON><PERSON><PERSON>", "ApplicationRequestInquiry": "<PERSON><PERSON><PERSON><PERSON>", "activityReport": "Laporan Aktiviti", "updateActivityReport": "Muat Naik Laporan Aktiviti", "organizationalActivitiesAppendix": "Lampiran aktiviti pertubuhan", "downloadReference": "<PERSON><PERSON>", "module": "<PERSON><PERSON><PERSON>", "noRecordDataLandingSearchTable": "Tiada rekod <PERSON>", "termsConditionsTitle": "Terma Penggunaan Sistem Elektronik Jabatan Pendaftaran Pertubuhan Malaysia (eROSES V2.0)", "cancellationDate": "<PERSON><PERSON><PERSON>", "typeOfNotice": "<PERSON><PERSON>", "descriptionNotice": "Keterangan Notis", "organizationCommittee": "J<PERSON><PERSON><PERSON><PERSON>", "organizationalDecisions": "Keputusan pertubuhan", "societyDocumentInfo": "1. <PERSON><PERSON><PERSON> pemilikan tanah\n2. Bil utiliti\n3. <PERSON><PERSON><PERSON> (b<PERSON><PERSON><PERSON>) jika ada\n4. Surat kebenaran jabatan jika pertubuhan dibawah jawabatan kerajaan", "RegisteraNewOrganization": "<PERSON><PERSON><PERSON>", "nonCitizenDocumentInfo": "Salinan Keputusan Carian Status Kebankrapan Jabatan Insolvensi bagi individu yang telah dilepaskan dari status bankrap", "hadMaksimumWangDalamTangan": "<PERSON><PERSON><PERSON><PERSON>", "morethan": "le<PERSON>h da<PERSON>", "lessthan": "kurang da<PERSON>", "insertLink": "<PERSON><PERSON><PERSON><PERSON>", "appointment": "Surat Lantikan", "appointmentInfo": "Maklumat Pelantikan", "loginMyDigitalId": "Log Masuk/Daftar dengan MyDigitallD", "verificationInProgress": "Verifikasi sedang di<PERSON>lank<PERSON>", "userDoesntExist": "No pengenalan tidak wujud dalam eRoses, sila minta pemegang no pengenalan untuk membuat akaun eRoses.", "recordOfActivityAvailable": "Terdapat rekod aktiviti", "completeReportUpload": "Sila lengkapkan laporan menggunakan templat yang disediakan dan muat naik gambar-gambar aktiviti pertubuhan.", "inactiveUserList": "Senarai Pengguna Tidak Aktif", "auditorAppointmentDate": "<PERSON><PERSON><PERSON>", "appointmentDateAudit": "<PERSON><PERSON><PERSON>", "phoneDigitLimitWarning": "Sila masukkan nombor telefon Malaysia yang sah (maksimum 12 digit dan bermula dengan +60).", "blacklistedMsg": "Pengguna telah disenarai hitam", "appointmentLetter": "Surat Pelantikan", "appealPeriod": "Tempoh Rayuan", "jawatankuasaConfirmation": "<PERSON><PERSON> saya dengan ini mengesahkan bahawa semua Ahli Jawatankuasa bagi pertubuhan di atas tidak hilang kelayakan memegang jawatan di bawah Seksyen 9A Akta Pertubuhan 1966. <PERSON>a set<PERSON>nya mengaku bahawa keterangan yang diberikan di atas adalah sesungguhnya benar.", "userStatusActive": "Aktif", "userStatusPending": "Menunggu <PERSON>", "userStatusInactive": "<PERSON><PERSON><PERSON>", "userStatusDeactivated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sejarahAkaun": "<PERSON><PERSON><PERSON>", "klikUntukLihatSenaraiPendaftaran": "Klik untuk lihat senarai pendaftaran", "userIdRequired": "ID Pengguna diperlukan", "pengesahanPerubahanStatus": "Pengesahan Perubahan Status", "confirmActiveUser": "Pengguna yang aktif boleh log masuk ke dalam sistem. <PERSON>kah anda pasti untuk menetapkan status sebagai Aktif?", "confirmInactiveUser": "Pengguna yang tidak aktif tidak boleh log masuk. <PERSON>kah anda pasti untuk menetapkan status sebagai Tidak Aktif?", "confirmDeactivateUser": "Pengguna yang dinyahaktifkan tidak boleh log masuk perlu mendaftar akaun baru. Adakah anda pasti untuk menyahaktifkan pengguna ini?", "confirmDeleteUser": "<PERSON><PERSON>h anda pasti untuk memadam pengguna ini?", "deleteUserSuccess": "<PERSON><PERSON><PERSON> berjaya dipadam", "deleteUserError": "<PERSON>l memadam pengguna", "trainingName": "<PERSON><PERSON>", "trainingDescription": "<PERSON><PERSON><PERSON><PERSON>", "trainingLevel": "<PERSON><PERSON><PERSON>", "trainingDuration": "<PERSON><PERSON><PERSON>", "trainingStatus": "Status Latihan", "trainingExplanation": "Keterangan <PERSON>han", "trainingObjective": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trainingPoster": "<PERSON><PERSON>", "chapterTitle": "Tajuk Bab", "chapterMedia": "Video at<PERSON> <PERSON><PERSON><PERSON>", "youtubeLink": "Link Youtube", "chapterDescription": "Keterangan Medium", "trainingQuizNo": "No Bilangan Quiz", "trainingMinScore": "Tahap <PERSON>", "question": "Soalan", "answer": "<PERSON><PERSON><PERSON>", "bil": "Bil", "correctAnswer": "<PERSON><PERSON><PERSON>", "noFasal": "No. Fasal", "tajukfasal": "Tajuk <PERSON>", "yangDipertua": "<PERSON>", "yangDipertuaCawangan": "Yang Dipertua Cawangan", "timbalanYangDipertua": "<PERSON><PERSON><PERSON>", "timbalanYangDipertuaCawangan": "<PERSON><PERSON><PERSON>", "naibPengerusiCawangan": "<PERSON><PERSON>", "naibPresidenCawangan": "<PERSON><PERSON>", "naibYangDipertuaCawangan": "<PERSON><PERSON>", "bendahariKehormatCawangan": "<PERSON><PERSON><PERSON>", "penolongBendahariKehormatCawangan": "Penolong Bendahari <PERSON>", "ahliJawatankuasaBiasa": "<PERSON><PERSON>", "ahliJawatankuasaBiasaCawangan": "<PERSON><PERSON>", "parentLiquidationWaitingList": "<PERSON><PERSON><PERSON> pem<PERSON>n induk", "BELUM DIHANTAR": "<PERSON><PERSON>", "ajkAppointmentList": "<PERSON><PERSON><PERSON>K", "documentUploadRequired": "Muat naik dokumen diperlukan apabila nyatakan pendapatan", "submitKuiri": "<PERSON><PERSON>", "declarationInvalid": "<PERSON><PERSON><PERSON>", "registrationFailed": "Pendaft<PERSON> gagal", "sessionExpired": "Sesi tamat tempoh - sila log masuk semula", "sessionExpiredTitle": "Sesi Tamat Tempoh", "noPermissionToAccessResource": "Anda tidak mempunyai kebenaran untuk mengakses sumber ini", "accessForbiddenTitle": "<PERSON><PERSON><PERSON>", "registerErrorTitle": "<PERSON><PERSON>", "JPNError": "Ralat: <PERSON><PERSON>, pen<PERSON><PERSON> tidak ber<PERSON>. <PERSON>la cuba semula selepas tempoh 4 jam", "chronologyOfComplaints": "Kronologi Cadangan/Aduan", "flowToJPPMDivisionState": "<PERSON><PERSON><PERSON> ke bahagian/negeri JPPM", "flowToOfficerAction": "Alirkan ke tindakan pegawai", "infoPaymentPegawaiAwam": "<PERSON><PERSON><PERSON><PERSON> pegawai awam telah <PERSON>.", "infoPaymentPegawaiHarta": "<PERSON><PERSON><PERSON><PERSON> pegawai harta telah direko<PERSON>.", "infoPaymentCawanganPegawaiAwam": "<PERSON><PERSON><PERSON><PERSON> cawangan pegawai awam telah <PERSON>.", "infoPaymentCawanganPegawaiHarta": "<PERSON><PERSON><PERSON><PERSON> cawangan pegawai harta telah dire<PERSON>.", "carianMaklumat": "<PERSON><PERSON> ma<PERSON>", "NomborFasal": "Nombor Fasal", "NamaFasal": "<PERSON><PERSON>", "createAuditor": "<PERSON><PERSON><PERSON>", "feedbackPhoneNoErr": "Nombor telefon mestilah antara 10 & 8 digit termasuk +60", "roleConfirmation": "<PERSON><PERSON>h anda pasti untuk mengemaskini peranan ini?", "roleConfirmationCreate": "<PERSON><PERSON>h anda pasti mahu mencipta peranan ini?", "paymentResultProcessing": "Memproses Keputusan Pembayaran", "paymentResultPleaseWait": "Sila tunggu sementara kami mendapatkan maklumat pembayaran anda...", "noRecordForStatusBranch": "Cawangan tiada dalam rekod. Sila hubungi JPPM Negeri untuk menyemak status pertubuhan anda.", "laporan": "<PERSON><PERSON><PERSON>", "dashboardStatistik": "Dashboard Statistik", "statistik": "Statistik", "analisis": "<PERSON><PERSON><PERSON>", "pengurusanPelaporan": "<PERSON><PERSON><PERSON><PERSON>", "tabPertubuhan": "<PERSON><PERSON><PERSON><PERSON>", "tabPentadbiran": "Pentadbiran", "tabPemerkasaan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tabPenguatkuasaan": "Penguatkuasaan", "tabApplikasiMobile": "Aplikasi Mobil", "failedLoadDashboardError": "Gagal memuatkan dashboard. Sila cuba lagi nanti.", "loadingDashboard": "Memuatkan dashboard...", "preparingDashboard": "Sedang menyediakan dashboard...", "noDashboardFoundError": "Tiada dashboard yang sepadan dengan kriteria anda.", "kategoriStatistik": "Kategori Statistik", "jenisStatistik": "<PERSON><PERSON>", "kategori": "<PERSON><PERSON><PERSON>", "jenisLaporan": "<PERSON><PERSON>", "meetingYear": "<PERSON><PERSON>", "dateOfAppointmentLetter": "<PERSON><PERSON><PERSON> Lantikan", "appliedYear": "<PERSON><PERSON>", "submissionYear": "<PERSON><PERSON>", "decisionYear": "<PERSON><PERSON>", "landing_mainSlide_1_titleTop": "Selamat Datang ke", "landing_mainSlide_1_description": "Sistem eROSES \nversi 2.0", "landing_mainSlide_1_btn": "<PERSON><PERSON> lagi", "landing_mainSlide_2_description": "<PERSON><PERSON><PERSON>", "landing_mainSlide_2_titleBottom": "<PERSON><PERSON><PERSON> pen<PERSON> per<PERSON> terb<PERSON>k", "landing_mainSlide_2_btn": "Info <PERSON>n<PERSON>", "landing_mainSlide_3_description": "<PERSON><PERSON><PERSON> untuk \nMembantu Anda", "landing_mainSlide_3_titleBottom": "<PERSON><PERSON><PERSON> anda untuk kemudahan dan sokongan", "landing_secSlide_1_title": "<PERSON><PERSON><PERSON><PERSON>", "landing_secSlide_1_desc": "Panduan pendaftaran pertubuhan", "landing_secSlide_2_title": "Kriteria", "landing_secSlide_2_desc": "<PERSON>yarat-syarat pendaftaran per<PERSON>uhan", "landing_secSlide_3_title": "<PERSON><PERSON><PERSON><PERSON>", "landing_secSlide_3_desc": "Buku Panduan Asas eROSES", "landing_secSlide_4_title": "<PERSON><PERSON><PERSON><PERSON>", "landing_secSlide_4_desc": "Soalan Lazim", "landing_bacaan_tab_1": "<PERSON><PERSON><PERSON>", "landing_bacaan_tab_2": "<PERSON><PERSON><PERSON>", "landing_bacaan_tab_3": "Aktiviti", "landing_bacaan_tab_4": "<PERSON><PERSON><PERSON>", "landing_logos_desc_1": "Pendaftar Pertubuhan berkaitan Nelayan di bawah Menteri Pertanian dan Keterjaminan Ma<PERSON>an", "landing_logos_desc_2": "Pendaftar dan pengawal selia pertubuhan sukan di bawah Kementerian Belia dan Sukan (KBS)", "landing_logos_desc_3": "Pendaftar dan pengawal selia <PERSON>em<PERSON>badanan Pemegang <PERSON> di Bawah Jaba<PERSON> Perdana Menteri", "landing_logos_desc_4": "Pendaftar dan pengawal selia per<PERSON>uhan veteran tentera di bawah Kementerian Pertahanan", "landing_logos_desc_5": "Pendaftar dan kawal selia Kesatuan Pekerja di bawah Kementerian Sumber Manusia", "landing_logos_desc_6": "Pendaftar dan pengawal selia syarikat Malaysia", "landing_logos_desc_7": "Pendaftar dan pengawal selia pertubuhan belia di bawah Kementerian Belia dan Sukan (KBS)", "landing_logos_desc_8": "Pendaftar persatuan ibu bapa dan guru seko<PERSON> di Bawah Kementerian Pendidikan", "landing_logos_desc_9": "Pendaftar Organisasi be<PERSON>", "takwim_title": "Takwim Aktiviti Jabatan Pertubuhan Malaysia (JPPM)", "takwim_subtitle": "Takwim Aktiviti JPPM", "takwim_desc": "Terokai pelbagai acara yang akan diadakan sepanjang tahun oleh JPPM. Temui acara yang sesuai dengan keperluan anda dan berpotensi memberikan inspirasi serta sokongan untuk meningkatkan keberkesanan pengurusan persatuan anda.", "takwim_sidebar_nextEvent": "<PERSON><PERSON><PERSON>", "takwim_sidebar_viewFull": "<PERSON><PERSON>", "monday": "<PERSON>in", "tuesday": "<PERSON><PERSON><PERSON>", "wednesday": "<PERSON><PERSON>", "thursday": "<PERSON><PERSON><PERSON>", "friday": "Juma<PERSON>", "saturday": "Sabtu", "sunday": "<PERSON><PERSON>", "CreateEvent": "<PERSON><PERSON><PERSON>", "fax": "faks", "DATA_CREATED": "Data berjaya dicipta", "DATA_UPDATED": "<PERSON> ber<PERSON><PERSON>", "DATA_FOUND": "<PERSON> berjaya di<PERSON>", "NO_DATA_FOUND": "Tiada data dijumpai", "DATA_DELETED": "Data berjaya dipadam", "EMAIL_SENT": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "PAYMENT_MADE": "Pembayaran berjaya dibuat", "PAYMENT_CANCELLED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "NO_DATA_FOUND_BY_CRITERIA": "Tiada data dijumpai berdasarkan kriteria", "OPERATION_COMPLETED": "<PERSON>si berjaya di<PERSON>", "RETRIEVAL_OK": "<PERSON><PERSON><PERSON><PERSON>", "PDF_GENERATED": "PDF berjaya dijana", "APPLICATION_SENT": "<PERSON><PERSON><PERSON><PERSON> telah dihantar untuk kel<PERSON>an", "SOCIETY_NAME_TAKEN": "<PERSON><PERSON>an telah wujud", "SOCIETY_NAME_AVAILABLE": "<PERSON><PERSON> boleh dig<PERSON>", "SOCIETY_CREATED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON> dicipta", "SOCIETY_UPDATED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "SOCIETY_DELETED": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a dipadam", "SOCIETY_FOUND": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "SOCIETY_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> dengan id yang diberikan tidak dapat dijumpai", "SOCIETY_APPROVED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "SOCIETY_REJECTED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "SOCIETY_CANCELLED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "SOCIETY_SUBMITTED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "SOCIETY_DRAFT_SAVED": "<PERSON><PERSON> <PERSON> ber<PERSON>a disimpan", "SOCIETY_PUBLISHED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "SOCIETY_UNPUBLISHED": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a dibatal terbit", "SOCIETY_ACTIVATED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "SOCIETY_DEACTIVATED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "SOCIETY_SUSPENDED": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a dig<PERSON>", "SOCIETY_REINSTATED": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON>", "BRANCH_CREATED": "Cawanga<PERSON> ber<PERSON>a dicipta", "BRANCH_UPDATED": "Cawanga<PERSON> be<PERSON><PERSON><PERSON>", "BRANCH_DELETED": "Cawangan berjaya dipadam", "BRANCH_FOUND": "Cawangan ber<PERSON>a di<PERSON>", "BRANCH_APPROVED": "Cawangan ber<PERSON>a <PERSON>", "BRANCH_REJECTED": "Cawangan be<PERSON><PERSON>a <PERSON>", "BRANCH_SUBMITTED": "Cawanga<PERSON> ber<PERSON>a <PERSON>", "COMMITTEE_CREATED": "<PERSON><PERSON><PERSON><PERSON><PERSON> berjaya dicipta", "COMMITTEE_UPDATED": "<PERSON><PERSON><PERSON><PERSON><PERSON> ber<PERSON>a <PERSON>", "COMMITTEE_DELETED": "<PERSON><PERSON><PERSON><PERSON><PERSON> berjaya dipadam", "COMMITTEE_FOUND": "J<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a di<PERSON>ai", "COMMITTEE_APPROVED": "J<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a <PERSON>", "COMMITTEE_REJECTED": "<PERSON><PERSON><PERSON><PERSON><PERSON> ber<PERSON>a di<PERSON>", "COMMITTEE_SUBMITTED": "<PERSON><PERSON><PERSON><PERSON><PERSON> ber<PERSON>a <PERSON>", "AMENDMENT_CREATED": "<PERSON><PERSON><PERSON> ber<PERSON>a dicipta", "AMENDMENT_UPDATED": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "AMENDMENT_DELETED": "<PERSON><PERSON><PERSON> ber<PERSON>a dipadam", "AMENDMENT_FOUND": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "AMENDMENT_APPROVED": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "AMENDMENT_REJECTED": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "AMENDMENT_SUBMITTED": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "PROPERTY_OFFICER_APPLICATION_CREATED": "<PERSON><PERSON><PERSON><PERSON> pegawai harta berjaya dicipta", "PROPERTY_OFFICER_APPROVED": "Peg<PERSON><PERSON> harta ber<PERSON>a <PERSON>", "PROPERTY_OFFICER_UPDATED": "Pegawai harta dike<PERSON>", "PROPERTY_OFFICER_APPLICATION_ALREADY_DELETED": "<PERSON><PERSON><PERSON><PERSON> pegawai harta telah dipadam", "PROPERTY_OFFICER_APPLICATION_DELETED": "<PERSON><PERSON><PERSON><PERSON> pegawai harta berjaya dipadam", "DATA_NOT_FOUND": "Data tidak dijumpai", "DATA_NOT_FOUND_WITH_CRITERIA": "Data tidak dijumpai berdasarkan kriteria", "DATA_ALREADY_EXISTS": "Data telah wujud", "DATA_CREATE_FAILED": "Gagal mencipta data", "DATA_UPDATE_FAILED": "Gagal mengemaskini data", "DATA_DELETE_FAILED": "Gagal memadam data", "DATABASE_ERROR": "Ralat pangkalan data berlaku", "NULL_POINTER_EXCEPTION": "Pengecualian null pointer berl<PERSON>u", "UNEXPECTED_ERROR": "<PERSON><PERSON> tidak dijangka berlaku", "USER_NOT_AUTHENTICATED": "Pengguna tidak disahkan", "TEMPLATE_NOT_FOUND": "Templat tidak dijumpai", "TEMPLATE_ALREADY_EXIST": "Templat sudah wujud", "EMAIL_NOT_SENT": "<PERSON><PERSON> t<PERSON>", "FILE_NOT_ATTACHED": "Tiada fail di<PERSON><PERSON>an", "S3_UPLOAD_FAILED": "Gagal memuat naik ke S3", "PAYMENT_FAILED": "<PERSON><PERSON><PERSON><PERSON> gagal", "INVALID_CODE": "Kod tidak sah", "INVALID_USER": "Pengguna tidak sah", "INVALID_REQUEST": "Permintaan tidak sah", "INTERNAL_SERVER_ERROR": "<PERSON><PERSON> p<PERSON>an", "MISSING_REQUEST_DTO": "DTO permintaan hilang", "CODE_ALREADY_EXISTS": "Kod telah wujud", "PROCESS_DOCUMENT_FAILED": "Gagal memproses dokumen", "MISSING_DATA_REQUEST": "Data hilang dalam permintaan", "USER_ALREADY_BLACKLISTED": "Pengguna sudah disenarai hitam", "NO_BLACKLIST_POLITIC_SOCIETY": "Permintaan tidak sah: Pertubuhan politik tidak boleh disenarai hitam", "USER_IS_BLACKLISTED": "Pengguna disenarai hitam", "USER_ALREADY_IS_MEMBER_OF_SOCIETY": "Pengguna sudah menjadi ahli <PERSON>an", "DOCUMENT_NOT_FOUND": "Dokumen tidak dijumpai", "DUPLICATE_MEETING_DATE_TYPE": "Mesyuarat dengan tarikh dan jenis yang sama sudah wujud", "POSTING_NOT_FOUND": "Siaran tidak dijumpai", "POSTING_ALREADY_EXISTS": "<PERSON><PERSON> sudah wujud", "POSTING_CREATE_FAILED": "<PERSON><PERSON> mencipta siaran", "POSTING_UPDATE_FAILED": "<PERSON><PERSON> siaran", "POSTING_DELETE_FAILED": "<PERSON><PERSON> memadam siaran", "POSTING_PUBLISH_FAILED": "<PERSON>l men<PERSON> siaran", "POSTING_UNPUBLISH_FAILED": "Gagal membatal terbit siaran", "INVALID_CATEGORY_FOR_SECRETARY": "Kategori tidak sah untuk set<PERSON>", "CANNOT_UPDATE_SUBMITTED_POSTING": "<PERSON><PERSON>k boleh mengemas<PERSON> siaran yang telah dihantar", "ALREADY_REPORTED_TODAY": "Sudah melaporkan siaran ini hari ini", "POSTING_REVIEW_ALREADY_SUBMITTED": "<PERSON><PERSON><PERSON> siaran sudah dihantar", "REJECTION_COMMENT_REQUIRED": "<PERSON><PERSON> penolakan diperlukan", "SOCIETY_NOT_FOUND_ID": "<PERSON><PERSON><PERSON><PERSON> dengan id yang diberikan tidak dapat ditemui", "NULL_BRANCH_COMMITTEE": "Id jawata<PERSON><PERSON><PERSON> cawangan tidak boleh kosong", "BRANCH_COMMITTEE_NOT_FOUND": "Jawatankuasa cawangan dengan id yang diberikan tidak dapat dijumpai", "COMMITTEE_NOT_FOUND": "Jawatankuasa dengan id yang diberikan tidak dapat dijumpai", "COMMITTEE_TASK_EXIST": "Tugas jawatankuasa untuk modul sudah wujud", "COMMITTEE_TASK_NOT_EXIST": "Tugas jawatankuasa untuk modul tidak wujud", "COMMITTEE_TASK_ALREADY_ENABLED": "Tugas jawatankuasa sudah diakti<PERSON>kan", "COMMITTEE_TASK_MODULE_NOT_ENABLED": "Tugas jawatankuasa belum diakti<PERSON>kan", "COMMITTEE_TASK_FOR_MODULE_IS_NOT_APPLICABLE_FOR_BRANCH": "Tugas jawatankuasa untuk modul tidak dibenarkan untuk cawangan", "NON_ADDITIONAL_INFORMATION": "Tiada maklumat tambahan tersedia", "BRANCH_NOT_FOUND": "Cawangan tidak dijumpai", "TRUSTEE_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> amanah tidak dijumpai", "TRUSTEE_ALREADY_EXISTS": "<PERSON><PERSON><PERSON><PERSON> amanah sudah wujud", "TRUSTEE_CREATE_FAILED": "<PERSON><PERSON> mencipta pemegang amanah", "TRUSTEE_UPDATE_FAILED": "<PERSON><PERSON> menge<PERSON><PERSON> pemegang amanah", "TRUSTEE_DEACTIVATE_FAILED": "<PERSON><PERSON> menyahakti<PERSON><PERSON> pemegang amanah", "TRUSTEE_CREATE_AUDIT_TRAIL_FAILED": "<PERSON><PERSON><PERSON><PERSON> amanah dicipta, tetapi gagal mencipta jejak audit", "TRUSTEE_UPDATE_AUDIT_TRAIL_FAILED": "<PERSON><PERSON><PERSON><PERSON> am<PERSON>, tetapi gagal mencipta jejak audit", "PUBLIC_OFFICER_CREATE_FAILED": "<PERSON><PERSON> mencipta pegawai awam", "PUBLIC_OFFICER_CREATE_AUDIT_FAILED": "Pegawai awam dicipta tetapi gagal memasukkan log audit", "PUBLIC_OFFICER_NOT_FOUND": "Pegawai awam tidak dijumpai", "PUBLIC_OFFICER_NOT_FOUND_WITH_ID": "Pegawai awam tidak dijumpai", "PUBLIC_OFFICER_UPDATE_FAILED": "<PERSON><PERSON> men<PERSON> pegawai awam", "PUBLIC_OFFICER_AUDIT_TRAIL_FAILED": "<PERSON><PERSON> mencipta rekod jejak audit", "PUBLIC_OFFICER_ALREADY_SUBMITTED": "<PERSON><PERSON><PERSON><PERSON> awam sudah dihantar", "PUBLIC_OFFICER_DELETE_FAILED": "<PERSON><PERSON> memadam pegawai awam", "PUBLIC_OFFICER_AUDIT_LOG_FAILED": "Gagal mencipta log audit", "PUBLIC_OFFICER_APPLICATION_STATUS_UPDATE_FAILED": "Gagal mengemaskini status permohonan pegawai awam", "RO_APPROVAL_CREATE_FAILED": "<PERSON><PERSON> <PERSON><PERSON><PERSON> RO", "INVALID_APPROVAL_STATUS": "Hanya DILULUSKAN dan DITOLAK dibenarkan", "PROPERTY_OFFICER_NOT_FOUND": "Pegawai harta tidak dijumpai", "PROPERTY_OFFICER_APPLICATION_NOT_FOUND_WITH_ID": "<PERSON><PERSON><PERSON><PERSON> pegawai harta tidak dijumpai", "PROPERTY_OFFICER_CREATE_FAILED": "<PERSON><PERSON> mencipta pegawai harta", "PROPERTY_OFFICER_DEACTIVATE_FAILED": "<PERSON>l menyahaktifkan pegawai harta sebelumnya", "PROPERTY_OFFICER_APPROVAL_STATUS_UPDATE_FAILED": "Gagal mengemaskini status kelulusan untuk permohonan pegawai harta", "UNSUPPORTED_STATUS": "Status tidak disokong", "UPDATE_OPERATION_NOT_ALLOWED": "Operasi kemaskini tidak dibenark<PERSON>", "PROPERTY_OFFICER_UPDATE_FAILED": "<PERSON><PERSON> menge<PERSON> pegawai harta", "INVALID_REQUEST_UPDATE_BOTH": "Permintaan tidak sah, anda boleh mengemaskini kod status permohonan atau pegawai tetapi bukan kedua-duanya", "DRAFT_PROPERTY_OFFICER_DELETE_ONLY": "<PERSON><PERSON> draf permohonan pegawai harta boleh dipadam", "PROPERTY_OFFICER_APPLICATION_UPDATE_FAILED": "Gagal mengemaskini permohonan pegawai harta", "BLACKLIST_USER_NOT_FOUND": "Pengguna disenarai hitam tidak dijumpai", "_comment_additional_messages": "=== Mesej <PERSON> ===", "PASSWORD_RESET_SUCCESS": "<PERSON>a la<PERSON>an berjaya diteta<PERSON>kan semula", "LOGOUT_TRIGGER_COMPLETED": "Pencetus log keluar selesai", "USER_LOGGED_OUT_SUCCESSFULLY": "Pengguna berjaya log keluar", "USER_UPDATED_SUCCESSFULLY": "<PERSON><PERSON><PERSON> ber<PERSON>a <PERSON>", "EMAIL_TEMPLATE_CREATED_SUCCESSFULLY": "<PERSON><PERSON><PERSON> <PERSON><PERSON> ber<PERSON>a dicipta", "EMAIL_TEMPLATE_UPDATED_SUCCESSFULLY": "<PERSON>mp<PERSON> <PERSON><PERSON> ber<PERSON><PERSON>", "NOTIFICATION_CREATED_SUCCESSFULLY": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a dicipta", "NOTIFICATION_UPDATED_SUCCESSFULLY": "<PERSON><PERSON><PERSON><PERSON> be<PERSON><PERSON><PERSON>", "USER_ADDED_SUCCESSFULLY": "Pengguna berjaya ditambah", "SMS_SENT_SUCCESSFULLY": "SMS berjaya di<PERSON>tar", "PAGE_MANAGEMENT_ADDED_SUCCESSFULLY": "<PERSON><PERSON><PERSON><PERSON> halaman berjaya ditambah", "PAGE_MANAGEMENT_UPDATED_SUCCESSFULLY": "<PERSON><PERSON><PERSON><PERSON> halaman ber<PERSON><PERSON>", "ALL_NOTIFICATION_EMAILS_SENT_SUCCESSFULLY": "<PERSON><PERSON><PERSON> emel notif<PERSON> ber<PERSON>a <PERSON>", "SOME_NOTIFICATION_EMAILS_SENT_SUCCESSFULLY": "<PERSON><PERSON><PERSON><PERSON> emel not<PERSON><PERSON> ber<PERSON>a <PERSON>", "ALL_NOTIFICATION_EMAILS_FAILED": "<PERSON><PERSON><PERSON> emel notif<PERSON> gagal di<PERSON>tar", "USER_LOGGED_IN_SUCCESSFULLY": "Pengguna berjaya log masuk", "FORGOT_PASSWORD_SUCCESS": "<PERSON><PERSON><PERSON><PERSON>", "OTP_SENT_SUCCESSFULLY": "OTP berjaya <PERSON>", "USER_APPROVAL_APPROVED_SUCCESSFULLY": "<PERSON><PERSON><PERSON><PERSON> pengguna telah berjaya di<PERSON>", "USER_APPROVAL_REJECTED_SUCCESSFULLY": "<PERSON><PERSON><PERSON><PERSON> pengguna telah berjaya di<PERSON>", "OTP_INVALID_MESSAGE": "Kod yang dimasukkan tidak betul. Sila cuba lagi", "OTP_VERIFIED_SUCCESSFULLY": "OTP berjaya di<PERSON>", "OTP_ALREADY_USED": "OTP telah digunakan", "OTP_EXPIRED": "<PERSON>d telah tamat tempoh, sila minta kod baharu", "OTP_MODULE_TYPE_INVALID": "<PERSON><PERSON> modul tidak sah", "OTP_IS_VALID": "OTP adalah sah", "USER_UPDATE_APPROVED_SUCCESSFULLY": "Kemas<PERSON> pengguna telah berjaya <PERSON>", "USER_UPDATE_REJECTED_SUCCESSFULLY": "Kemas<PERSON> pengguna telah ber<PERSON>a di<PERSON>", "ALL_EMAILS_SENT": "<PERSON><PERSON><PERSON> emel notif<PERSON> ber<PERSON>a <PERSON>", "PARTIAL_EMAILS_SENT": "<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a: em<PERSON> te<PERSON>", "POSTING_CREATED": "<PERSON><PERSON> ber<PERSON>a dicipta", "POSTING_UPDATED": "<PERSON><PERSON> ber<PERSON>", "POSTING_DELETED": "<PERSON><PERSON> berjaya dipadam", "POSTING_PUBLISHED": "<PERSON><PERSON> ber<PERSON>a <PERSON>", "POSTING_UNPUBLISHED": "Siaran berjaya dibatal terbit", "POSTING_FOUND": "<PERSON><PERSON> ber<PERSON>a <PERSON>", "POSTINGS_FOUND": "<PERSON><PERSON> ber<PERSON>a <PERSON>", "POSTING_REPORTED": "<PERSON><PERSON> ber<PERSON>a <PERSON>", "POSTING_REPORTS_FOUND": "<PERSON><PERSON><PERSON> siaran ber<PERSON>a di<PERSON>", "POSTING_REVIEWS_FOUND": "<PERSON><PERSON><PERSON> siaran ber<PERSON>a di<PERSON>", "POSTING_EXPIRED_FOUND": "<PERSON><PERSON> tamat tempoh berjaya di<PERSON>ai", "ENGAGEMENT_STATS_FOUND": "Statistik penglibatan ber<PERSON>a di<PERSON>", "ENGAGEMENT_RECORDED": "Pengli<PERSON><PERSON> ber<PERSON>a <PERSON>", "POSTING_REVIEW_SUBMITTED": "<PERSON><PERSON><PERSON> siaran ber<PERSON>a <PERSON>", "NOTIFICATION_MARKED_AS_READ": "Notifikasi berjaya ditanda sebagai dibaca", "ALL_NOTIFICATIONS_MARKED_AS_READ": "<PERSON><PERSON><PERSON> notifika<PERSON> berjaya ditanda sebagai dibaca", "POSTING_USER_FOUND": "<PERSON><PERSON><PERSON> siaran berjaya di<PERSON>ai", "POSTING_PRE_PUBLISH": "Pra Terbit siaran berjaya di<PERSON>ai", "SUBMIT_SUCCESS": "<PERSON><PERSON><PERSON><PERSON>", "QUERY_SUBMIT_SUCCESS": "<PERSON><PERSON><PERSON> be<PERSON><PERSON>", "COMMITTEE_TASK_CREATED": "<PERSON><PERSON> jawatankuasa berjaya dicipta", "COMMITEE_FOUND": "J<PERSON><PERSON><PERSON><PERSON> ber<PERSON>a di<PERSON>ai", "COMMITTEE_TASK_DEACTIVATED": "<PERSON><PERSON> jawatank<PERSON>sa ber<PERSON>a <PERSON>", "MEETING_CREATED": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> dicipta", "MEETING_UPDATED": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "TRUSTEE_CREATED": "<PERSON><PERSON><PERSON><PERSON> amanah berjaya dicipta", "TRUSTEE_UPDATED": "<PERSON><PERSON><PERSON><PERSON> amanah ber<PERSON>", "TRUSTEE_DEACTIVATED": "<PERSON><PERSON><PERSON><PERSON> amanah ber<PERSON>a <PERSON>", "_comment_additional_error_messages": "--- <PERSON><PERSON><PERSON> ---", "USER_NOT_FOUND": "Pengguna tidak dijumpai", "OLD_PASSWORD_NOT_MATCH": "<PERSON>a la<PERSON>an lama tidak sepadan", "NEW_PASSWORD_SAME_AS_OLD": "<PERSON>a laluan baharu tidak boleh sama dengan kata laluan lama", "IDENTIFICATION_NUMBER_ALREADY_EXISTS": "Nombor pengenalan ini sudah wujud", "EMAIL_ADDRESS_ALREADY_EXISTS": "<PERSON><PERSON><PERSON> emel ini sudah wujud", "IDENTIFICATION_NOT_FOUND_IN_SYSTEM": "Nombor pengenalan tidak wujud dalam sistem. <PERSON>la daftar akaun baharu", "ACCOUNT_ALREADY_ACTIVATED": "<PERSON><PERSON><PERSON> anda telah di<PERSON>. <PERSON>la hubungi pihak berkaitan", "ACCOUNT_DEACTIVATED_REJECTED": "<PERSON><PERSON><PERSON> anda telah disekat/dinyah<PERSON><PERSON><PERSON><PERSON>. <PERSON>la hubungi pihak berkaitan", "INVALID_CREDENTIALS": "Nombor Kad <PERSON> dan Kata <PERSON> tidak sepadan. Sila cuba lagi", "USER_ALREADY_EXISTS": "Pengguna sudah wujud", "INVALID_SERVICE": "Perkhidmatan tidak sah", "GENERIC_ERROR": "<PERSON><PERSON>", "USER_IDENTIFICATION_ALREADY_EXISTS": "Nombor pengenalan pengguna ini sudah wujud", "INVALID_LOGIN_TYPE": "Jenis log masuk tidak sah", "PERMISSION_DENIED": "<PERSON><PERSON><PERSON>", "UNAUTHORIZED_ACCESS": "<PERSON><PERSON><PERSON> tidak <PERSON>", "INVALID_USER_GROUP": "Kumpulan pengguna tidak sah", "NO_RECORDS_FOUND": "Tiada rekod dijumpai", "NO_PARENT_ID_FOUND": "Tiada id induk dijumpai", "INVALID_STATUS": "Status tidak sah", "PAGE_MANAGEMENT_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> halaman tidak dijumpai", "EMAIL_TEMPLATE_NOT_FOUND": "Templat emel tidak dijumpai", "VALIDATION_FAILED": "<PERSON><PERSON><PERSON> gagal", "DATABASE_UPDATE_ERROR": "<PERSON><PERSON> pang<PERSON>an data berlaku semasa mengemaskini pengguna", "NULL_DATA_ENCOUNTERED_USER": "Data kosong ditemui semasa mengemas<PERSON> pengguna", "UNEXPECTED_UPDATE_ERROR": "<PERSON><PERSON> tidak dijangka berlaku semasa menge<PERSON> pengguna", "DATABASE_EMAIL_TEMPLATE_ERROR": "<PERSON><PERSON> pang<PERSON>an data berlaku semasa mencipta templat emel", "NULL_DATA_EMAIL_TEMPLATE": "Data kosong ditemui semasa mencipta templat emel", "DATABASE_NOTIFICATION_ERROR": "<PERSON><PERSON> pang<PERSON> data berlaku semasa mencipta notifikasi", "MYDIGITALID_LOGIN_URL_FAILED": "Gagal menjana URL log masuk myDigitalId", "MYDIGITALID_TOKEN_EXCHANGE_FAILED": "<PERSON><PERSON>kar kod untuk token", "COMMITTEE_CREATE_AUDIT_TRAIL_FAILED": "<PERSON><PERSON><PERSON><PERSON><PERSON> dicipta, tetapi gagal mencipta jejak audit", "COMMITTEE_INTERNAL_SERVER_ERROR": "<PERSON><PERSON> pang<PERSON>an data berlaku semasa mendapatkan jawatankuasa", "COMMITEE_EMPTY": "Data kosong dijumpai semasa mendapatkan jawatankuasa", "COMMITEE_UNEXPECTEDERROR": "<PERSON><PERSON> tidak dijangka berlaku semasa mendapatkan jawatankuasa", "COMMITEE_SEARCH_NOT_FOUND": "Tiada jawatankuasa dijumpai dengan kriteria carian yang diberikan", "CURRENT_SECRETARY_CANNOT_APPLY": "<PERSON><PERSON><PERSON><PERSON> semasa tidak boleh memohon jawatan setia<PERSON>ha", "APPEAL_CREATE_FAILED": "Tidak dapat mencipta kes rayuan", "APPEAL_UPDATE_FAILED": "<PERSON><PERSON> rayuan tidak dapat dike<PERSON>", "APPEAL_REMOVE_FAILED": "<PERSON><PERSON>d kes rayuan gagal dikemaskini", "PROPERTY_OFFICER_APPLICATION_CREATE_FAILED": "<PERSON><PERSON> mencipta permohonan pegawai harta", "PROPERTY_OFFICER_APPLICATION_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> pegawai harta tidak dijumpai", "PROPERTY_OFFICER_RO_UPDATE_FAILED": "<PERSON><PERSON> menge<PERSON> pegawai harta ro", "NO_TEMPLATE_FOUND": "Templat tidak dijumpai", "ALL_EMAILS_FAILED": "<PERSON><PERSON> men<PERSON>tar sebarang emel notif<PERSON>", "TITLE_REQUIRED": "Tajuk diperlukan", "USER_ID_REQUIRED": "ID pengguna diperlukan", "FIELD_NOT_FOUND": "<PERSON><PERSON><PERSON> medan ralat memproses", "PAGE_NUMBER_VALIDATION": "Nombor halaman mestilah lebih besar da<PERSON>ada atau sama dengan 1", "PAGE_SIZE_VALIDATION": "<PERSON><PERSON> halaman mestilah lebih besar daripada atau sama dengan 1", "SOCIETY_ID_NULL": "ID Pertubuhan tidak boleh kosong", "_comment_missing_success_messages": "--- <PERSON><PERSON><PERSON> dari eroses-society ---", "COMMITTEE_TASK_ACTIVATED": "<PERSON><PERSON> ber<PERSON>", "SOCIETY_COMMITTEE_TASK_MODULE_ENABLED": "Tugas Jawatankuasa Pertubuhan telah diaktifkan", "SOCIETY_COMMITTEE_TASK_MODULE_DISABLED": "Tugas Jawatankuasa Pertubuhan telah dinyahaktifkan", "BRANCH_COMMITTEE_TASK_MODULE_ENABLED": "Tugas Jawatankuasa Cawangan telah diaktifkan", "BRANCH_COMMITTEE_TASK_MODULE_DISABLED": "Tugas Jawatankuasa Cawangan telah dinyahaktifkan", "APPEAL_CREATED": "<PERSON><PERSON> rayuan berjaya dicipta", "APPEAL_UPDATED": "<PERSON><PERSON> ray<PERSON> ber<PERSON>", "APPEAL_REMOVED": "<PERSON><PERSON> rayuan ber<PERSON>", "PUBLIC_OFFICER_CREATED": "<PERSON><PERSON><PERSON><PERSON> awam berjaya dicipta", "PUBLIC_OFFICER_UPDATED": "<PERSON><PERSON><PERSON><PERSON> awam ber<PERSON>", "PUBLIC_OFFICER_REMOVED": "<PERSON><PERSON><PERSON><PERSON> awam ber<PERSON>a <PERSON>", "PUBLIC_OFFICER_APPROVED": "<PERSON><PERSON><PERSON><PERSON> awam ber<PERSON>a <PERSON>", "PUBLIC_OFFICER_RETRIEVED": "<PERSON><PERSON><PERSON><PERSON> awam berjaya <PERSON>h", "BRANCH_PUBLIC_OFFICER_RETRIEVED": "<PERSON>eg<PERSON><PERSON> awam cawangan berjaya dipero<PERSON>h", "PUBLIC_OFFICER_COUNT_RETRIEVED": "<PERSON><PERSON> pegawai awam berjaya <PERSON>ero<PERSON>h", "BRANCH_PUBLIC_OFFICER_COUNT_RETRIEVED": "<PERSON><PERSON> pegawai awam cawangan berjaya dipero<PERSON>h", "_comment_missing_error_messages": "--- <PERSON><PERSON><PERSON> dari eroses-society ---", "COMMITTEE_DETAIL_NOT_FILLED": "Butiran jawatankuasa tidak diisikan dengan lengkap", "NON_CITIZEN_APPLICATION_NOT_SUBMITTED": "Terdapat permohonan ahli jawatan kuasa bukan warganegara belum dihantar", "COMMITTEE_TASK_MODULE_ALREADY_ENABLED": "Tugas jawatankuasa sudah diakti<PERSON>kan", "MEETING_NOT_CREATED": "Mesyuarat tidak dicipta", "_comment_notification_only_messages": "--- <PERSON><PERSON><PERSON> Notifikasi ---", "_comment_complaint_specific_messages": "--- <PERSON><PERSON><PERSON> ---", "_comment_profile_update_messages": "=== Mesej <PERSON> Profil ===", "PROFILE_UPDATED_SUCCESSFULLY": "<PERSON>il <PERSON><PERSON><PERSON>", "PROFILE_UPDATE_FAILED": "<PERSON><PERSON> menge<PERSON> profil", "INVALID_PROFILE_DATA": "Data profil yang diberikan tidak sah", "PROFILE_NOT_FOUND": "<PERSON>il tidak di<PERSON>", "UNAUTHORIZED_PROFILE_UPDATE": "Tidak dibenarkan untuk mengemaskini profil ini", "PROFILE_UPDATE_VALIDATION_ERROR": "<PERSON><PERSON><PERSON> profil gagal", "reorder": "<PERSON><PERSON>"}