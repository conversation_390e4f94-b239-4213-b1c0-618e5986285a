import { useEffect, useMemo, useState } from "react";
import Stack from "@mui/material/Stack";
import { useTranslation } from "react-i18next";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import {
  Button,
  CircularProgress,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Theme,
  useMediaQuery,
  useTheme,
} from "@mui/material";
import { Change, diffWordsWithSpace } from "diff";
import { useNavigate, useParams } from "react-router-dom";
import SectionHeader from "../../../components/header/section/SectionHeader";
import { toCapitalCase } from "../../../helpers/string";
import { ButtonPrimary, ButtonText } from "../../../components/button";
import { SemakPerlembagaanTitle } from "./SemakPerlembagaanTitle";
import { FormatFasal1 } from "./FormatFasal1";
import { Organization } from "../../../types/organization";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import FasalContent from "../../../components/FasalContent";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../api";
import { getLocalStorage } from "../../../helpers/utils";
import { ApplicationStatus, ConstitutionType } from "../../../helpers/enums";
import { useSelector } from "react-redux";
import { DokumenIcon } from "../../../components/icons";
import DownloadIcon from "@mui/icons-material/Download";
import { useDownloadAndExportConstitutions } from "@/helpers/hooks/useDownloadConstitutions";

const labelStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const highlighDiffWord = (text1: string, text2: string): string => {
  const words1 = text1.split(" ");
  const words2 = text2.split(" ");
  const maxLength = Math.max(words1.length, words2.length);

  let result = "";

  for (let i = 0; i < maxLength; i++) {
    const word1 = words1[i] || "";
    const word2 = words2[i] || "";

    if (word1 !== word2) {
      result += `<span style="color: #FFE65899;">${word2 || ""}</span> `;
    } else {
      result += `${word2} `;
    }
  }

  return result.trim();
};

export const SemakPerlembagaan = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md"));

  // const [clauseContentId, setClauseContentId] = useState(1);
  const [currentFasal, setCurrentFasal] = useState(0);
  const [beforeFasal, setBeforeFasal] = useState([]);
  const [afterFasal, setAfterFasal] = useState([]);
  //  const [baseFasal,setBaseFasal] = useState([])
  const amendmentId = getLocalStorage("amendmentId", null);

  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const { id } = useParams();
  const fasalRedux = useSelector((state: { fasal: any }) => state.fasal.data);
  const isView = fasalRedux.IsViewPindaan || null;
  const { data: clauseContentBefore, isLoading: loadingBeforeData } = useCustom(
    {
      url: `${API_URL}/society/constitutioncontent/get`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
        query: {
          societyId: id,
          ...(isView
            ? {
                oldConstitution: true,
                amendmentId: amendmentId,
              }
            : {
                status: ApplicationStatus["AKTIF"],
              }),
        },
      },
      queryOptions: {
        enabled: !!id,
      },
    }
  );

  const { data: clauseContentAfter, isLoading: loadingAfterData } = useCustom({
    url: `${API_URL}/society/constitutioncontent/get`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: id,
        // status: ApplicationStatus["AKTIF"],
        amendmentId: amendmentId,
      },
    },
    queryOptions: {
      enabled: !!id,
    },
  });

  const clauseContentDataBefore = clauseContentBefore?.data?.data?.data || [];
  const clauseContentDataAfter = clauseContentAfter?.data?.data?.data || [];

  const { data: constitutionData, isLoading: isConstitutionLoading } =
    useCustom({
      url: `${API_URL}/society/admin/constitutionTypeWithClauseContent/list`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
    });

  const { data: societyData, isLoading: isSocietyLoading } = useCustom({
    url: `${API_URL}/society/${id}/`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const allConstitutions = constitutionData?.data?.data || [];
  const constitutionContentRegisterRequest = getLocalStorage(
    "constitutionContentRegisterRequest",
    null
  );

  //  console.log(originalConstuitionType)
  // console.log(constitutionType)

  const constitutionTypeBefore =
    societyDataRedux?.constitutionType || ConstitutionType.IndukNGO[1];

  const constitutionTypeAfter =
    fasalRedux.currentAmendmentConstitutionType || ConstitutionType.IndukNGO[1];

  const isConstitutionTypeBeforeAfterSame =
    societyDataRedux?.constitutionType ===
    fasalRedux.currentAmendmentConstitutionType;

  const generatePerlembagaanBefore = () => {
    if (constitutionTypeBefore) {
      const updatedConstitutions = allConstitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const existingItem = clauseContentDataBefore.find(
              (item: any) => item.clauseContentId === clause.id
            );
            if (existingItem) {
              return {
                ...clause,
                content: existingItem?.description,
                description: existingItem?.description,
              };
            }
            return { ...clause, description: clause?.content };
          }
        );
        return {
          ...item,
          clauseContents: updatedClauseContents,
        };
      });
      updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionTypeBefore) {
          setBeforeFasal(item.clauseContents);
        }
      });
    } else {
      setBeforeFasal([]);
    }
  };

  const generatePerlembagaanAfter = () => {
    if (constitutionTypeAfter) {
      const updatedConstitutions = allConstitutions?.map((item: any) => {
        const updatedClauseContents = item?.clauseContents?.map(
          (clause: any, index: any) => {
            const existingItem = clauseContentDataAfter.find(
              (item: any) => item.clauseContentId === clause.id
            );
            if (existingItem) {
              return {
                ...clause,
                description: existingItem?.description,
              };
            }
            return { ...clause, description: clause?.content };
          }
        );
        return {
          ...item,
          clauseContents: updatedClauseContents,
        };
      });
      updatedConstitutions?.map((item: any) => {
        if (item.name === constitutionTypeAfter) {
          setAfterFasal(item.clauseContents);
        }
      });
    } else {
      setAfterFasal([]);
    }
  };

  useEffect(() => {
    if (clauseContentDataBefore) {
      generatePerlembagaanBefore();
    }
    if (clauseContentAfter) {
      generatePerlembagaanAfter();
    }
  }, [
    constitutionTypeBefore,
    constitutionTypeAfter,
    constitutionData,
    clauseContentAfter,
    clauseContentBefore,
  ]);

  const isLoading =
    loadingAfterData || loadingBeforeData || isConstitutionLoading;

  const { getConstitutionsFile, isLoadingDownloadConstitutions } =
    useDownloadAndExportConstitutions();

  const hideIndex = clauseContentDataAfter.findIndex(
    (p: any) => p.hideConstitution == true
  );

  const hideId = clauseContentDataAfter?.[hideIndex]?.clauseContentId ?? null;

  // handle semak bebas content (BEFORE / AFTER)
  // =====================================================

  //  const finalSenaraiFasalBEFORE = [...senaraiFasal, ...getAllBebasTambahContentbefore];

  // const getAllBebasTambahContentAFTER = clauseContentDataAfter
  //   .filter((item: any) => item.clauseContent === null)
  //   .map((item: any, index: number) => ({
  //     name: `${t(item?.clauseName)}`,
  //     description: item.description,
  //     clauseNo: item?.clauseNo,
  //     id: item?.id,
  //     constitutionTypeId:item?.constitutionTypeId
  //   }));

  // const finalFasalContentAFTER = [...afterFasal, ...getAllBebasTambahContentAFTER];

  // if (isClauseContentDataIsLoading || isConstitutionLoading) {
  //   return (
  //     <Box
  //       sx={{
  //         display: "flex",
  //         justifyContent: "center",
  //         alignItems: "center",
  //         minHeight: "300px",
  //       }}
  //     >
  //       <CircularProgress />
  //     </Box>
  //   );
  // }
  //  console.log(beforeFasal)
  //  console.log(afterFasal)

  const getAllBebasTambahContentBEFORE = clauseContentDataBefore
    .filter((item: any) => item.clauseContent === null)
    .map((item: any, index: number) => ({
      title: `${t("clause")} ${item?.clauseNo}: ${t(item?.clauseName)}`,
      content: item.description,
      clauseNo: item?.clauseNo,
      description: item.description,
      id: item?.id,
      name: item?.clauseName,
    }));

  const getAllBebasTambahContentAFTER = clauseContentDataAfter
    .filter((item: any) => item.clauseContent === null)
    .map((item: any, index: number) => ({
      title: `${t("clause")} ${item?.clauseNo}: ${t(item?.clauseName)}`,
      content: item.description,
      clauseNo: item?.clauseNo,
      description: item.description,
      id: item?.id,
      name: item?.clauseName,
    }));

  // console.log(getAllBebasTambahContentBEFORE)

  const finalBeforeFasal = [...beforeFasal, ...getAllBebasTambahContentBEFORE];
  const finalAfterFasal = [...afterFasal, ...getAllBebasTambahContentAFTER]; 
  const isTambahBebas =
    getAllBebasTambahContentBEFORE.length > 0 ||
    getAllBebasTambahContentAFTER.length > 0;
 

  return (
    <Box
      gap={2}
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          overflowY: "scroll",
        }}
      >
        <Typography sx={labelStyle}>{t("reviewConstitution")}</Typography>

        {!isLoading &&
          finalBeforeFasal.length > 0 &&
          finalAfterFasal.length > 0 && (
            <FasalContent
              back
              hideId={hideId}
              isConstitutionTypeSame={isConstitutionTypeBeforeAfterSame}
              fasalContent={finalBeforeFasal}
              HaveBebasContent={isTambahBebas}
              fasalContentActions={() => (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    rowGap: "0.5rem",
                    marginTop: "1rem",
                  }}
                >
                  <Button
                    startIcon={<DokumenIcon />}
                    variant="outlined"
                    sx={{ borderRadius: "0.625rem", width: "100%" }}
                    onClick={() => id && getConstitutionsFile(id, 2)}
                  >
                    Papar Perlembagaan Asal
                  </Button>
                  <Button
                    startIcon={<DownloadIcon />}
                    variant="outlined"
                    sx={{ borderRadius: "0.625rem", width: "100%" }}
                    onClick={() => id && getConstitutionsFile(id, 1)}
                  >
                    Muat Turun Perlembagaan Asal
                  </Button>
                </div>
              )}
              compareData={finalAfterFasal}
              compareDataActions={() => (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    rowGap: "0.5rem",
                    marginTop: "1rem",
                  }}
                >
                  <Button
                    startIcon={<DokumenIcon />}
                    variant="outlined"
                    sx={{ borderRadius: "0.625rem", width: "100%" }}
                    onClick={() =>
                      id && getConstitutionsFile(id, 2, amendmentId)
                    }
                  >
                    Papar Perlembagaan Pindaan
                  </Button>
                  <Button
                    startIcon={<DownloadIcon />}
                    variant="outlined"
                    sx={{ borderRadius: "0.625rem", width: "100%" }}
                    onClick={() =>
                      id && getConstitutionsFile(id, 1, amendmentId)
                    }
                  >
                    Muat Turun Perlembagaan Pindaan
                  </Button>
                </div>
              )}
            />
          )}
      </Box>
    </Box>
  );
};

export default SemakPerlembagaan;
