import { Navigate, Outlet, Route } from "react-router-dom";
// import PengurusanPertubuhanPembubaranList from "../../pages/pengurusan-pertubuhan-internal/pembubaran";
// import KeputusanCawanganList from "../../pages/pengurusan-pertubuhan-internal/pembubaran/KeputusanCawangan";
import PengurusPertubuhanInternalLayout from "../../pages/pengurusan-pertubuhan-internal/PengurusPertubuhanLayout";
// import PengurusanPertubuhanMainList from "../../pages/pengurusan-pertubuhan-internal/main";
// import PengurusPertubuhanInternalMainLayout from "../../pages/pengurusan-pertubuhan-internal/Layout";
// import FormPembubaranInternal from "../../pages/pengurusan-pertubuhan-internal/pembubaran/Form";
// import KeputusanPertubuhan from "../../pages/pengurusan-pertubuhan-internal/pembubaran";
import KeputusanPertubuhanNav from "../../pages/pengurusan-pertubuhan-internal/View/KeputusanPertubuhanNav";
import MaklumatPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan";
import SemakanUmum from "../../pages/pengurusan-pertubuhan-internal/SemakanUmum";
import PenyelenggaraPertubuhan from "../../pages/pengurusan-pertubuhan-internal/SelenggaraPertubuhan";
import KeputusanCawangan from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan";
import Rayuan from "../../pages/pengurusan-pertubuhan-internal/Rayuan";
import SenaraiHitam from "../../pages/pengurusan-pertubuhan-internal/SenaraiHitam";
import Kuiri from "../../pages/pengurusan-pertubuhan-internal/Kuiri";
import KeputusanIndukKelulusanSemak from "../../pages/pengurusan-pertubuhan-internal/keputusaninduk-semak";
import SenaraiAjkSemak from "../../pages/pengurusan-pertubuhan-internal/keputusaninduk-semak/views/SenaraiAjkSemak";
import PaparanAjk from "../../pages/pengurusan-pertubuhan-internal/keputusaninduk-semak/views/PaparanAjk";
import KeputusanIndukKelulusan from "../../pages/pengurusan-pertubuhan-internal/keputusanInduk";
import KeputusanIndukPembubaran from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pembubaran";
import KeputusanIndukPembaharuanSetiausaha from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pembaharuan-setiausaha";
import KeputusanIndukPembaharuanSetiausahaMigrasi from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pembaharuan-setiausaha-migrasi";
import KeputusanIndukBukanWargaNegara from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-bukan-warganegara";
import KeputusanIndukPegawaiAwam from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pegawai-awam";
import KeputusanIndukPegawaiHarta from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pegawai-harta";
import KeputusanCawangan_Pendaftaran from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/Pendaftaran";
import KeputusanCawangan_LanjutMasa from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/LanjutMasa";
import KeputusanCawangan_PindaanNamaDanAlamat from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PindaanNamaDanAlamat";
import KeputusanCawangan_PegawaiAwam from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PegawaiAwam";
import KeputusanCawangan_PegawaiHarta from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PegawaiHarta";
import KeputusanCawangan_Pembubaran from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/Pembubaran";
import KeputusanCawangan_PermohonanBukanWarganegara from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PermohonanBukanWarganegara";
import KeputusanCawangan_PendaftaranTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PendaftaranTab";
import KeputusanCawangan_LanjutMasaTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/LanjutMasaTab";
import KeputusanCawangan_PindaNamaAlamatTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PindaNamaAlamat";
import KeputusanCawangan_PegawaiAmTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PegawaiAmTab";
import KeputusanCawangan_PegawaiHartaTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PegawaiHartaTab";
import KeputusanCawangan_PembubaranTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PembubaranTab";
import KeputusanCawangan_PermohonanBukanWarganegaraTab from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PermohonanBukanWarganegaraTab";
import MaklumatPertubuhan_Induk_Pertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_NamaPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Nama";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_AlamatPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Alamat";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_AlamatSuratPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/AlamatSuratMenyurat";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_StatusPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Status";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_KategoriPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Kategori";
import MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_TarafPertubuhan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/kemaskini/Taraf";

import MaklumatInduk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk";
import MaklumatPertubuhan_Induk_SemakAjk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/views/Ajk/SemakAJK";
import MaklumatPertubuhan_Induk_PaparAjk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/views/Ajk/PaparAJK";
import MaklumatPertubuhan_Induk_AhliBukanWarganegara from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/AhliBukanWarganegara";
import MaklumatPertubuhan_Induk_PertukaranSetiausaha from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/PertukaranSetiausaha";
import MaklumatPertubuhan_Induk_PindaanPerlembagaan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/PindaanPerlembagaan";
import MaklumatPertubuhan_Induk_Pembubaran from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pembubaran";
import MaklumatPertubuhan_Induk_Rayuan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Rayuan";
import MaklumatPertubuhan_Induk_PenyataTahunan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/PenyataTahunan";

import MaklumatPertubuhan_Cawangan_Maklumat from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan";
import MaklumatPertubuhan_Cawangan_SemakAjk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/views/Ajk/SemakAJK";
import MaklumatPertubuhan_Cawangan_PaparAjk from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/views/Ajk/PaparAJK";
import MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/kemaskini";
import MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini_NamaCawangan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/kemaskini/Nama";
import MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini_StatusCawangan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/Cawangan/kemaskini/Status";

import MaklumatPertubuhan_Cawangan_PemegangJawatan_PegawaiAwam from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PemegangJawatan/PegawaiAwam";
import MaklumatPertubuhan_Cawangan_PemegangJawatan_Juruaudit from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PemegangJawatan/Juruaudit";
import MaklumatPertubuhan_Cawangan_PemegangJawatan_PemegangAmanah from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PemegangJawatan/PemegangAmanah";
import MaklumatPertubuhan_Cawangan_PemegangJawatan_PegawaiHarta from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PemegangJawatan/PegawaiHarta";
import MaklumatPertubuhan_Cawangan_AhliBukanWarganegara from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/AhliBukanWarganegara";

import MaklumatCawangan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan";
import MaklumatPertubuhan_Cawangan_SijilMigrasiCawangan from "../../pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/SijilMigrasiCawangan";
import KeputusanCawangan_PermohonanBukanWarganegaraJawatanKuasa from "../../pages/pengurusan-pertubuhan-internal/KeputusanCawangan/PermohonanBukanWarganegara/JawatanKuasa";

import SemakanUmumPendaftaranLama from "../../pages/pengurusan-pertubuhan-internal/SemakanUmum/noPendaftaranLama";
import SemakanUmumPemegegangJawatan from "../../pages/pengurusan-pertubuhan-internal/SemakanUmum/pemegangJawatan";

import { TabProvider } from "../../contexts/tabProvider";
import KeputusanIndukPindaanUndangUndangInduk from "../../pages/pengurusan-pertubuhan-internal/keputusan-induk-pindaan-undang-undang-induk";
import RayuanKelulusanSemak from "../../pages/pengurusan-pertubuhan-internal/Rayuan/views";
import KuiriPendaftaranPertubuhanIndukTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/PendaftaranPertubuhanInduk";
import KuiriPendaftaranCawanganTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/PendaftaranCawangan";
import KuiriPindaanUndangUndangIndukTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/PindaanUndangUndangInduk";
import KuiriRayuanTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/Rayuan";
import KuiriPembaharuanSetiausahaTab from "../../pages/pengurusan-pertubuhan-internal/Kuiri/PembaharuanSetiausaha";
import BranchPaparanAjkBukanWn from "@/pages/pengurusan-pertubuhan-internal/KeputusanCawangan/Pendaftaran/views/BranchPaparanAjkBukanWn";
import BranchPaparanAJK from "@/pages/pengurusan-pertubuhan-internal/KeputusanCawangan/Pendaftaran/views/BranchPaparanAjk";
import KeputusanIndukExternalAgency from "@/pages/pengurusan-pertubuhan-internal/keputusan-external-agency";
import KuiriRayuanKelulusanSemak from "@/pages/pengurusan-pertubuhan-internal/Kuiri/Rayuan/views";
import PenyataTahunanBranchDetail from "@/pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/cawangan/PenyataTahunan";
import KuiriPindaanDetails from "@/pages/pengurusan-pertubuhan-internal/Kuiri/Pindaan";
import { PertubuhanAccordions } from "@/pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/PertubuhanAccordions";
import { PertubuhanMesyuaratById } from "@/pages/pengurusan-pertubuhan-internal/MaklumatPertubuhan/maklumatInduk/Pertubuhan/views/PertubuhanMesyuaratById";

// const routes = [
//   {
//     path: "form-pembubaran",
//     page: <FormPembubaranInternal />,
//   },
//   {
//     path: "pembubaran-cawangan",
//     page: <KeputusanCawanganList title="Keputusan Cawangan" />,
//   },
//   {
//     path: "rayuan",
//     page: <PengurusanPertubuhanPembubaranList title="Jenis Rayuan" />,
//   },
//   {
//     path: "rayuan",
//     page: <div />,
//   },
//   {
//     path: "senarai-hitam",
//     page: <PengurusanPertubuhanPembubaranList title="Senarai Hitam" />,
//   },
//   {
//     path: "kuiri",
//     page: <PengurusanPertubuhanPembubaranList title="Jenis Kuiri" />,
//   },
//   {
//     path: "maklumat-pertubuhan",
//     page: <PengurusanPertubuhanPembubaranList title="Maklumat Pertubuhan" />,
//   },
//   {
//     path: "penyelenggara-pertubuhan",
//     page: (
//       <PengurusanPertubuhanPembubaranList title="Penyelenggara Pertubuhan" />
//     ),
//   },
// ];

export const pengurus_pertubuhan_internal = {
  routes: (
    <Route
      path="pengurus-pertubuhan"
      element={<PengurusPertubuhanInternalLayout />}
    >
      <Route index element={<Navigate to="keputusan-pertubuhan" replace />} />
      <Route path="keputusan-pertubuhan" element={<KeputusanPertubuhanNav />}>
        <Route index element={<Navigate to="keputusan-induk" replace />} />
        <Route path="keputusan-induk" element={<KeputusanIndukKelulusan />} />

        {/*Tab page*/}
        <Route
          index
          path="keputusan-induk/kelulusan/:id"
          element={<KeputusanIndukKelulusanSemak />}
        />
        <Route
          index
          path="keputusan-induk/external-agency/:id"
          element={<KeputusanIndukExternalAgency />}
        />
        <Route
          index
          path="keputusan-induk/pembubaran/:id"
          element={<KeputusanIndukPembubaran />}
        />
        <Route
          index
          path="keputusan-induk/permohonan-bukan-warganegara/:id"
          element={<KeputusanIndukBukanWargaNegara />}
        />
        <Route
          index
          path="keputusan-induk/pindaan-perlembagaan/:amendmentId/:id"
          element={<KeputusanIndukPindaanUndangUndangInduk />}
        />
        <Route
          index
          path="keputusan-induk/pegawai-awam/:id"
          element={<KeputusanIndukPegawaiAwam />}
        />
        <Route
          index
          path="keputusan-induk/pegawai-awam/:id"
          element={<KeputusanIndukPegawaiAwam />}
        />
        <Route
          index
          path="keputusan-induk/pegawai-harta/:id"
          element={<KeputusanIndukPegawaiHarta />}
        />
        <Route
          index
          path="keputusan-induk/pembaharuan-setiausaha/:id"
          element={<KeputusanIndukPembaharuanSetiausaha />}
        />
        <Route
          index
          path="keputusan-induk/pembaharuan-setiausaha-migrasi/:id"
          element={<KeputusanIndukPembaharuanSetiausahaMigrasi />}
        />

        <Route path="keputusan-cawangan" element={<Outlet />}>
          <Route element={<KeputusanCawangan />}>
            <Route index element={<KeputusanCawangan_PendaftaranTab />} />
            <Route
              path="lanjut-masa"
              element={<KeputusanCawangan_LanjutMasaTab />}
            />
            <Route
              path="pindaan-nama-dan-alamat"
              element={<KeputusanCawangan_PindaNamaAlamatTab />}
            />
            <Route
              path="pembubaran"
              element={<KeputusanCawangan_PembubaranTab />}
            />
            <Route
              path="pembubaran/:id/:branchId"
              element={<KeputusanIndukPembubaran />}
            />
            <Route
              path="permohonan-bukan-warganegara"
              element={<KeputusanCawangan_PermohonanBukanWarganegaraTab />}
            />
            <Route
              path="pegawai-awam"
              element={<KeputusanCawangan_PegawaiAmTab />}
            />
            <Route
              path="pegawai-harta"
              element={<KeputusanCawangan_PegawaiHartaTab />}
            />
          </Route>
          {/* ajk branch view */}
          <Route
            path="pendaftaran/:id/cawangan-paparan-ajk/:mid"
            element={<BranchPaparanAJK />}
          />
          {/* non-citizen ajk branch view */}
          <Route
            path="pendaftaran/:id/cawangan-paparan-ajk-bukan-wn/:mid"
            element={<BranchPaparanAjkBukanWn />}
          />
          <Route
            path="pendaftaran/:id"
            element={<KeputusanCawangan_Pendaftaran />}
          />
          <Route
            path="lanjut-masa/:id"
            element={<KeputusanCawangan_LanjutMasa />}
          />
          <Route
            path="pindaan-nama-dan-alamat/:id/:branchAmendmentId"
            element={<KeputusanCawangan_PindaanNamaDanAlamat />}
          />
          <Route
            path="pegawai-awam/:id"
            element={<KeputusanCawangan_PegawaiAwam />}
          />
          <Route
            path="pegawai-harta/:id"
            element={<KeputusanCawangan_PegawaiHarta />}
          />
          <Route
            path="pembubaran/:id"
            element={<KeputusanCawangan_Pembubaran />}
          />
          <Route
            path="permohonan-bukan-warganegara/:id"
            element={<KeputusanCawangan_PermohonanBukanWarganegara />}
          >
            <Route
              path="jawatan-kuasa/:ajkId"
              element={
                <KeputusanCawangan_PermohonanBukanWarganegaraJawatanKuasa />
              }
            />
          </Route>
        </Route>

        <Route path="rayuan" element={<Rayuan />} />
        <Route
          index
          path="rayuan/kelulusan/:id/:type"
          element={<RayuanKelulusanSemak />}
        />
        <Route path="senarai-hitam" element={<SenaraiHitam />} />
        <Route path="kuiri" element={<Outlet />}>
          <Route element={<Kuiri />}>
            <Route index element={<KuiriPendaftaranPertubuhanIndukTab />} />
            <Route
              path="pendaftaran-cawangan"
              element={<KuiriPendaftaranCawanganTab />}
            />
            <Route
              path="pindaan-undang-undang-induk"
              element={<KuiriPindaanUndangUndangIndukTab />}
            />
            <Route path="rayuan" element={<KuiriRayuanTab />} />
            <Route
              index
              path="rayuan/:id"
              element={<KuiriRayuanKelulusanSemak />}
            />

            <Route
              path="pembaharuan-setiausaha"
              element={<KuiriPembaharuanSetiausahaTab />}
            />
          </Route>
          <Route
            path="pendaftaran/:id"
            element={<KeputusanIndukKelulusanSemak />}
          />
          <Route
            path="pendaftaran-cawangan/:id"
            element={<KeputusanCawangan_Pendaftaran />}
          />
          <Route
            path="pindaan-undang-undang-induk/:amendmentId/:id"
            element={<KuiriPindaanDetails />}
          />
          <Route path="rayuan/:encodedId" element="Test" />
          <Route
            path="pembaharuan-setiausaha/:id"
            element={<KeputusanIndukPembaharuanSetiausaha />}
          />
        </Route>
        {/* ajk semak page */}
        <Route
          path="keputusan-induk/:id/senarai-ajk-semak"
          element={<SenaraiAjkSemak />}
        />
        {/* ajk paparan page */}
        <Route
          path="keputusan-induk/:id/paparan-ajk"
          element={<PaparanAjk />}
        />
      </Route>
      <Route
        path="maklumat-pertubuhan"
        element={
          <TabProvider>
            <MaklumatPertubuhan />
          </TabProvider>
        }
      >
        <Route index element={<Navigate to="induk" replace />} />
        <Route
          path="induk/pertubuhan/view/:id"
          element={<MaklumatPertubuhan_Induk_Pertubuhan />}
        >
          <Route index element={<PertubuhanAccordions />} />
          <Route path="mesyuarat/:meetingId" element={<PertubuhanMesyuaratById />} />
        </Route>
        <Route
          index
          path="induk/pertubuhan/ajk/:id"
          element={<MaklumatPertubuhan_Induk_SemakAjk />}
        />
        <Route
          index
          path="induk/pertubuhan/ajk/papar"
          element={<MaklumatPertubuhan_Induk_PaparAjk />}
        />
        <Route
          index
          path="induk/pertubuhan/ahli-bukan-warganegara/:societyId/:ajkId"
          element={<MaklumatPertubuhan_Induk_AhliBukanWarganegara />}
        />
        <Route
          index
          path="induk/pertubuhan/pertukaran-setiausaha/:id"
          element={<KeputusanIndukPembaharuanSetiausaha />}
        />
        <Route
          index
          path="induk/pertubuhan/pindaan-perlembagaan/:amendmentId/:societyId"
          element={<MaklumatPertubuhan_Induk_PindaanPerlembagaan />}
        />
        <Route
          index
          path="induk/pertubuhan/pembubaran"
          element={<MaklumatPertubuhan_Induk_Pembubaran />}
        />
        <Route
          index
          path="induk/pertubuhan/rayuan"
          element={<MaklumatPertubuhan_Induk_Rayuan />}
        />
        <Route
          index
          path="induk/pertubuhan/penyata-tahunan"
          element={<MaklumatPertubuhan_Induk_PenyataTahunan />}
        />

        <Route
          index
          path="induk/pertubuhan/kemaskini"
          element={<MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini />}
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/nama-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_NamaPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/alamat-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_AlamatPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/alamat-surat-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_AlamatSuratPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/status-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_StatusPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/kategori-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_KategoriPertubuhan />
          }
        />
        <Route
          index
          path="induk/pertubuhan/kemaskini/taraf-pertubuhan"
          element={
            <MaklumatPertubuhan_Induk_Pertubuhan_Kemaskini_TarafPertubuhan />
          }
        />
        <Route
          index
          path="induk/pindaan/:id"
          element={<MaklumatPertubuhan_Induk_Pembubaran />}
        />
        <Route
          index
          path="cawangan/maklumat/view"
          element={<MaklumatPertubuhan_Cawangan_Maklumat />}
        />
        <Route
          index
          path="cawangan/maklumat/ajk/papar"
          element={<MaklumatPertubuhan_Cawangan_PaparAjk />}
        />
        <Route
          index
          path="cawangan/maklumat/ajk"
          element={<MaklumatPertubuhan_Cawangan_SemakAjk />}
        />
        <Route
          index
          path="cawangan/maklumat/kemaskini"
          element={<MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini />}
        />
        <Route
          index
          path="cawangan/maklumat/kemaskini/nama-cawangan"
          element={
            <MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini_NamaCawangan />
          }
        />
        <Route
          index
          path="cawangan/maklumat/kemaskini/status-cawangan"
          element={
            <MaklumatPertubuhan_Cawangan_Maklumat_Kemaskini_StatusCawangan />
          }
        />

        <Route
          index
          path="cawangan/pegawai-awam"
          element={<MaklumatPertubuhan_Cawangan_PemegangJawatan_PegawaiAwam />}
        />
        <Route
          index
          path="cawangan/juruaudit"
          element={<MaklumatPertubuhan_Cawangan_PemegangJawatan_Juruaudit />}
        />
        <Route
          index
          path="cawangan/pemegang-amanah"
          element={
            <MaklumatPertubuhan_Cawangan_PemegangJawatan_PemegangAmanah />
          }
        />
        <Route
          index
          path="cawangan/pegawai-harta"
          element={<MaklumatPertubuhan_Cawangan_PemegangJawatan_PegawaiHarta />}
        />
        <Route
          index
          path="cawangan/ahli-bukan-warganegara"
          element={<MaklumatPertubuhan_Cawangan_AhliBukanWarganegara />}
        />
        <Route
          index
          path="cawangan/sijil-migrasi-cawangan"
          element={<MaklumatPertubuhan_Cawangan_SijilMigrasiCawangan />}
        />
        <Route
          index
          path="cawangan/pertubuhan/penyata-tahunan"
          element={<PenyataTahunanBranchDetail />}
        />
        <Route path="induk" element={<MaklumatInduk />} />
        <Route path="cawangan" element={<MaklumatCawangan />} />
      </Route>
      <Route path="semakan-umum" element={<SemakanUmum />}>
        <Route index element={<Navigate to="no-pendaftaran-lama" replace />} />
        <Route
          path="no-pendaftaran-lama"
          element={<SemakanUmumPendaftaranLama />}
        />
        <Route
          path="pemegang-jawatan"
          element={<SemakanUmumPemegegangJawatan />}
        />
      </Route>

      <Route
        path="maklumat-pertubuhan"
        element={
          <TabProvider>
            <MaklumatPertubuhan />
          </TabProvider>
        }
      />
      <Route
        path="penyelenggara-pertubuhan"
        element={<PenyelenggaraPertubuhan />}
      />
    </Route>
  ),
};
